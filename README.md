# Initialization.py
## Projectinfo
### attributes
1. dependency: 各*.v 文件的依赖
2. path: 各*.v 文件的物理地址和逻辑地址
3. files: 各*.v 文件的 vernac cmds(Coq code)
4. premises: 各*.v 文件的non-proof mode cmds

## ParsingCmds
初步解析项目内的*.v 文件获取 ProjectInfo
### functions
1. match_require: 匹配各*.v 文件的 require 语句
2. getImportedFile: 获取各*.v 文件的依赖文件
3. extract_premises_file: 提取各*.v 文件的non-proof mode cmds
4. **extract_proofs**(尚未修改完成):用于抓取已有的证明(提取中间的proof-mode 内容用于训练)