#!/bin/bash

# finetuning
# /workspace/ssd1/model/april/Qwen2.5-7B-Instruct-finetuning
# /workspace/ssd1/model/april/Qwen2.5-7B-finetuning
#
# base
# /workspace/ssd1/model/base/Qwen2.5-7B-Instruct
# /workspace/ssd1/model/base/Qwen2.5-7B


# TODO:
# (no instruct) ture lora + old-template-noRAG
# (no instruct) ture lora + old-template-RAG

export BASE_MODEL=/workspace/ssd1/model/base/Qwen2.5-7B-Instruct
export LORA_MODEL=/workspace/ssd1/model/new/Qwen2.5-7B-instruct-noRAG-true-lora
export RETRIVER_MODEL=/workspace/ssd1/model/retriver/paraphrase-multilingual-MiniLM-L12-v2
export VECTOR_DB=/workspace/ssd1/coqfinetuning/vectorDB/curated-prelude/
export TORCH_DEVICE=cuda:0
export RESULT_SUFFIX=$(basename "$LORA_MODEL")-redoSFT-noRAG

echo "testing on $(find $1 -name '*.v' | wc -l) files ..."

find $1 -name '*.v' | shuf > v-tasks
split -n l/8 --numeric-suffixes=0 --suffix-length=2 v-tasks test_tasks_

for i in {0..7}; do
    # zero‑pad i, i+8 and i+16 to two digits
    idx0=$(printf "%02d" $i)

    # run the three shards on GPU $i
    TORCH_DEVICE="cuda:$i" python ./test-driver.py $(cat "test_tasks_$idx0") &
done
