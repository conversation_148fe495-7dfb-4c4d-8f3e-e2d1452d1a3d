#!/usr/bin/fish

set models \
  Qwen2.5-7B-Instruct-finetuning \
  Qwen2.5-7B-Instruct-noRAG-finetuning \
  Qwen2.5-7B-Instruct \
  Qwen2.5-7B-finetuning \
  Qwen2.5-7B-noRAG-finetuning \
  Qwen2.5-7B \
  Qwen2.5-7B-prompt \
  Qwen2.5-7B-Instruct-prompt \
  Qwen2.5-7B-prompt \
  Qwen2.5-7B-Instruct-prompt \
  '-redoSFT-noRAG' \
  '-redoSFT-RAG' \



set root $argv[1]
echo "statistics from results in " $root

set -x MAX_ATTEMPTS 20

for model in $models
  echo $model
  python3 count-correct.py (find $root -name "*.v.$model")
  echo
end


