{"cells": [{"cell_type": "code", "execution_count": 3, "id": "d090a1d5", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['kind', 'name', 'definition', 'steps', 'cmds'])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import json \n", "with open ('./plf/Maps.v.json', 'r') as f:\n", "    data = json.load(f)\n", "data[0].keys()"]}, {"cell_type": "code", "execution_count": 6, "id": "680a1dfa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Example update_example1 ['Proof.', 'reflexivity.', 'Qed.']\n", "Example update_example2 ['Proof.', 'reflexivity.', 'Qed.']\n", "Example update_example3 ['Proof.', 'reflexivity.', 'Qed.']\n", "Example update_example4 ['Proof.', 'reflexivity.', 'Qed.']\n", "Lemma t_apply_empty ['Proof.', 'Admitted.']\n", "Lemma t_update_eq ['Proof.', 'Admitted.']\n", "Theorem t_update_neq ['Proof.', 'Admitted.']\n", "Lemma t_update_shadow ['Proof.', 'Admitted.']\n", "Theorem t_update_same ['Proof.', 'Admitted.']\n", "Theorem t_update_permute ['Proof.', 'Admitted.']\n", "Lemma apply_empty ['Proof.', 'intros.', 'unfold empty.', 'rewrite t_apply_empty.', 'reflexivity.', 'Qed.']\n", "Lemma update_eq ['Proof.', 'intros.', 'unfold update.', 'rewrite t_update_eq.', 'reflexivity.', 'Qed.']\n", "Theorem update_neq ['Proof.', 'intros A m x1 x2 v H.', 'unfold update.', 'rewrite t_update_neq.', '-', 'reflexivity.', '-', 'apply H.', 'Qed.']\n", "Lemma update_shadow ['Proof.', 'intros A m x v1 v2.', 'unfold update.', 'rewrite t_update_shadow.', 'reflexivity.', 'Qed.']\n", "Theorem update_same ['Proof.', 'intros A m x v H.', 'unfold update.', 'rewrite <- H.', 'apply t_update_same.', 'Qed.']\n", "Theorem update_permute ['Proof.', 'intros A m x1 x2 v1 v2.', 'unfold update.', 'apply t_update_permute.', 'Qed.']\n", "Lemma includedin_update ['Proof.', 'unfold includedin.', \"intros A m m' x vx H.\", 'intros y vy.', 'destruct (eqb_spec x y) as [Hxy | Hxy].', '-', 'rewrite Hxy.', 'rewrite update_eq.', 'rewrite update_eq.', 'intro H1.', 'apply H1.', '-', 'rewrite update_neq.', '+', 'rewrite update_neq.', '*', 'apply H.', '*', 'apply Hxy.', '+', 'apply Hxy.', 'Qed.']\n"]}], "source": ["for p in data:\n", "    print(p['kind'],p['name'],p['cmds'])"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}