from Initialization import Coq<PERSON><PERSON>rt, <PERSON>rem, Projectinfo
import argparse
import coq.coqtop as coqtop
import coq.runner
from loguru import logger
from pathlib import Path
import coq.sentences as sentences

class ProvingInFiles:
    def __init__(self) -> None:
        pass



class ProvingTheorems:
    def __init__(self,
                 projectinfo: Projectinfo) -> None:
        self.info = projectinfo

    @staticmethod
    def tacgen()->str: # unfinished
        return 'Admitted.'

    @staticmethod
    def proveTheoremInFile(vernac_cmds:list[sentences.Sentence],
                           top: coqtop.Coqtop,
                           filename:str):
        thm: Theorem | None = None
        proofs: list[Theorem] = []
        TIMEOUT = 30

        coqlib = runner.load_coq_project(Path(filename))
        version = top.find_coq(None, None)
        logger.info(f"using coq version: {version}")
                    
        [err, msg] = top.start(
            filename=filename,
            coqproject_args=coqlib,
            use_dune=False,
            dune_compile_deps=False,
            timeout=TIMEOUT,
            stderr_is_warning=True,
        )
        logger.info("coqtop start message", msg)
        assert err is None
        for sentence in vernac_cmds:
            cmd = sentence.text
            print(cmd)
            line, col = sentence.line, sentence.col

            # 执行到proofs开头
            _, _, before_state, _ = top.goals()
            ok, msg, _, err = top.advance(cmd, in_script=True)
            assert ok, logger.info(f'cmd:{cmd}, msg:{msg}, err:{err}')
            _, _, after_state, _ = top.goals()
            # print(msg,after_state)

            # start of a proof
            if before_state is None and after_state is not None:
                if not thm:
                    thm = Theorem()
                    try:
                        # loading meta data of current theorem
                        thm.kind, thm.name, thm.definition = sentences.proof_meta(cmd)
                        logger.info(f"+ working on {thm.kind} {thm.name} {thm.definition}")
                    except Exception as _:
                        logger.error(f"Failed to prove theorem {thm.name} in {filename} ")
                        thm = None
            
            # while proving !!! 需要进一步解析proof，生成proof后需要跳过原有的proof（测试数据集带有proof）
            else:
                if thm:
                    # 开始证明
                    retry = 0
                    
                    _, _, before_state, _ = top.goals()
                    ok, _, _, _ = top.advance('Admitted.', in_script=True)
                    print('Admitted.')
                    _, _, after_state, _ = top.goals()
                    
                    if after_state:

                        while after_state != None and retry < 10:
                            _, response, _, err = top.advance(cmd = ProvingTheorems.tacgen(), in_script=True)
                            retry += 1

                    logger.info(f"- done with theorem {thm.name}")
                    proofs.append(thm)
                    thm = None
                    continue
                    
                    
                    
                    

if __name__ == "__main__":
    parser = parser = argparse.ArgumentParser(description="Run Coq source file with configuration.")
    parser.add_argument("logical", type=str, help="name to the Coq project")
    parser.add_argument("physical", type=str, help="path to the Coq project")
    args = parser.parse_args()

    physical = Path(args.physical)
    logical = args.logical

    projectinfo = Projectinfo(
        physical,
        logical
    )

    projectinfo.get_dependency()
    

    test = ProvingTheorems(projectinfo=projectinfo)
    top = coqtop.Coqtop()
    # print(*list(test.info.premises.values())[1],sep='\n')

    # test.proveTheoremInFile(vernac_cmds = list(test.info.files_wo_proof.values())[0],
    #                         filename = list(test.info.files_wo_proof.keys())[0],
    #                          top = top )
    
    # print(*projectinfo.dependency.items(),sep='\n')
    # projectinfo.plot_dependency()

    # projectinfo.get_premises()
    # for filename, premises in projectinfo.premises.items():
    #     print('----------------'*3)
    #     print(filename)
    #     print()
    #     for premise in premises:
    #         print(premise)

