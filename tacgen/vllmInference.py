"""
vLLM-based inference engine for text generation.
Simple and clean interface for local model inference.
"""

import logging
from loguru import logger
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

try:
    from vllm import LLM, SamplingParams
    VLLM_AVAILABLE = True
except ImportError:
    VLLM_AVAILABLE = False
    logger.info("Warning: vLLM not available. Please install with: pip install vllm")


@dataclass
class ModelConfig:
    """Configuration for the language model"""
    model_path: str
    tensor_parallel_size: int = 1
    gpu_memory_utilization: float = 0.9
    max_model_len: int = 4096
    trust_remote_code: bool = True


@dataclass
class GenerationConfig:
    """Configuration for text generation"""
    temperature: float = 0.7
    top_p: float = 0.9
    max_tokens: int = 512
    num_return_sequences: int = 1
    stop_tokens: Optional[List[str]] = None


class VLLMInference:
    """
    Simple vLLM-based text generation interface.
    """

    def __init__(self, model_config: ModelConfig, generation_config: GenerationConfig):
        self.model_config = model_config
        self.generation_config = generation_config
        self.logger = logging.getLogger(__name__)

        if not VLLM_AVAILABLE:
            raise ImportError("vLLM is required but not installed. Please install with: pip install vllm")

        # Initialize vLLM model
        self.logger.info(f"Loading model from {model_config.model_path}")
        self.llm = LLM(
            model=model_config.model_path,
            tensor_parallel_size=model_config.tensor_parallel_size,
            gpu_memory_utilization=model_config.gpu_memory_utilization,
            max_model_len=model_config.max_model_len,
            trust_remote_code=model_config.trust_remote_code
        )

        # Set up default sampling parameters
        stop_tokens = generation_config.stop_tokens or []
        self.sampling_params = SamplingParams(
            temperature=generation_config.temperature,
            top_p=generation_config.top_p,
            max_tokens=generation_config.max_tokens,
            n=generation_config.num_return_sequences,
            stop=stop_tokens
        )

        self.logger.info("Model loaded successfully")

    def generate(self, prompt: str, custom_params: Optional[SamplingParams] = None) -> List[str]:
        """
        Generate text based on the given prompt.

        Args:
            prompt: Input text prompt
            custom_params: Optional custom sampling parameters

        Returns:
            List of generated text completions
        """
        params = custom_params or self.sampling_params

        self.logger.debug(f"Generating with prompt: {prompt[:100]}...")

        # Generate using vLLM
        outputs = self.llm.generate([prompt], params)

        results = []
        for output in outputs:
            for completion in output.outputs:
                text = completion.text.strip()
                if text:
                    results.append(text)

        return results

    def generate_batch(self, prompts: List[str], custom_params: Optional[SamplingParams] = None) -> List[List[str]]:
        """
        Generate text for multiple prompts in batch.

        Args:
            prompts: List of input prompts
            custom_params: Optional custom sampling parameters

        Returns:
            List of lists, each containing generated completions for corresponding prompt
        """
        params = custom_params or self.sampling_params

        self.logger.debug(f"Batch generating for {len(prompts)} prompts")

        # Generate using vLLM
        outputs = self.llm.generate(prompts, params)

        results = []
        for output in outputs:
            prompt_results = []
            for completion in output.outputs:
                text = completion.text.strip()
                if text:
                    prompt_results.append(text)
            results.append(prompt_results)

        return results

    def create_custom_params(self, **kwargs) -> SamplingParams:
        """
        Create custom sampling parameters.

        Args:
            **kwargs: Sampling parameter overrides

        Returns:
            SamplingParams object with custom settings
        """
        params = {
            'temperature': self.generation_config.temperature,
            'top_p': self.generation_config.top_p,
            'max_tokens': self.generation_config.max_tokens,
            'n': self.generation_config.num_return_sequences,
            'stop': self.generation_config.stop_tokens or []
        }

        # Override with custom parameters
        params.update(kwargs)

        return SamplingParams(**params)


def create_model_config(model_path: str, **kwargs) -> ModelConfig:
    """
    Create a ModelConfig with default values.

    Args:
        model_path: Path to the model
        **kwargs: Additional configuration overrides

    Returns:
        ModelConfig object
    """
    config = ModelConfig(model_path=model_path)

    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

    return config


def create_generation_config(**kwargs) -> GenerationConfig:
    """
    Create a GenerationConfig with default values.

    Args:
        **kwargs: Configuration overrides

    Returns:
        GenerationConfig object
    """
    config = GenerationConfig()

    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

    return config


# Example usage and testing
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)

    try:
        # Example: Create configurations
        model_config = create_model_config(
            model_path="/path/to/your/model",
            tensor_parallel_size=1,
            gpu_memory_utilization=0.8
        )

        generation_config = create_generation_config(
            temperature=0.7,
            max_tokens=256,
            num_return_sequences=2
        )

        # Initialize inference engine
        inference = VLLMInference(model_config, generation_config)

        # Example generation
        prompt = "Hello, how are you?"
        results = inference.generate(prompt)

        print("Generated responses:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result}")

    except Exception as e:
        print(f"Error: {e}")
        print("Please set the correct model path and ensure vLLM is installed.")
