"""
LLM与Coqtop交互的中间Agent
处理LLM生成的策略并与Coq进程交互
"""

import re
import time
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from enum import Enum

import coq.coqtop as coqtop
from tacgen.contextual_vllm import ContextualVLLMInference
from loguru import logger


class ProofStatus(Enum):
    """证明状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    STUCK = "stuck"


@dataclass
class CoqResponse:
    """Coq响应结果"""
    success: bool
    message: str
    goals: Optional[str] = None
    error: Optional[str] = None
    proof_completed: bool = False


@dataclass
class ProofStep:
    """证明步骤记录"""
    step_number: int
    tactic: str
    coq_response: CoqResponse
    timestamp: float
    llm_reasoning: Optional[str] = None


class CoqLLMAgent:
    """
    LLM与Coqtop交互的中间Agent
    """
    
    def __init__(self, 
                 contextual_inference: ContextualVLLMInference,
                 coqtop_instance: coqtop.Coqtop,
                 max_steps: int = 50,
                 timeout_per_step: int = 30):
        
        self.llm = contextual_inference
        self.coqtop = coqtop_instance
        self.max_steps = max_steps
        self.timeout_per_step = timeout_per_step
        
        # 证明状态
        self.current_proof_status = ProofStatus.NOT_STARTED
        self.proof_steps: List[ProofStep] = []
        self.current_theorem = ""
        
        # 设置专门的系统提示
        self._setup_coq_system_prompt()
        
        logger.info("CoqLLMAgent initialized")

    def _setup_coq_system_prompt(self):
        """设置Coq专用的系统提示"""
        system_prompt = """You are an expert Coq theorem prover. Your task is to generate valid Coq tactics to prove theorems.

IMPORTANT RULES:
1. Output ONLY the tactic command, nothing else
2. Do not include "Proof." or "Qed." 
3. Do not explain or comment
4. Use valid Coq syntax
5. Consider the current goal and hypotheses
6. If you're unsure, try basic tactics like: intros, simpl, reflexivity, induction, apply, rewrite

Common tactics:
- intros: introduce variables/hypotheses
- simpl: simplify expressions
- reflexivity: prove equality when both sides are the same
- induction: proof by induction
- apply: apply a theorem/hypothesis
- rewrite: rewrite using an equality
- split: split conjunction
- left/right: choose side of disjunction
- assumption: use an existing hypothesis

Example:
Goal: forall n : nat, 0 + n = n
Output: intros n.

Goal: 0 + n = n
Output: simpl.

Goal: n = n  
Output: reflexivity."""

        self.llm.set_system_prompt(system_prompt)

    def _extract_tactic_from_llm_output(self, llm_output: str) -> str:
        """从LLM输出中提取策略"""
        # 清理输出
        tactic = llm_output.strip()
        
        # 移除可能的markdown代码块
        if tactic.startswith("```"):
            lines = tactic.split('\n')
            tactic = '\n'.join(lines[1:-1]) if len(lines) > 2 else ""
        
        # 移除解释性文本，只保留策略
        lines = tactic.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('//'):
                # 确保以句号结尾
                if not line.endswith('.'):
                    line += '.'
                return line
        
        return "assumption."  # 默认策略

    def _get_current_goals(self) -> Optional[str]:
        """获取当前Coq目标"""
        try:
            _, _, goals, _ = self.coqtop.goals()
            if goals and hasattr(goals, 'goals') and goals.goals:
                # 格式化目标信息
                goal_text = []
                for i, goal in enumerate(goals.goals):
                    goal_text.append(f"Goal {i+1}:")
                    if hasattr(goal, 'hyps') and goal.hyps:
                        goal_text.append("Hypotheses:")
                        for hyp in goal.hyps:
                            goal_text.append(f"  {hyp}")
                    goal_text.append("============================")
                    goal_text.append(f"  {goal.goal}")
                    goal_text.append("")
                
                return '\n'.join(goal_text)
            return None
        except Exception as e:
            logger.error(f"Error getting goals: {e}")
            return None

    def _execute_tactic(self, tactic: str) -> CoqResponse:
        """执行Coq策略"""
        try:
            logger.info(f"Executing tactic: {tactic}")
            
            # 执行策略
            ok, message, _, error = self.coqtop.advance(tactic, in_script=True)
            
            if ok:
                # 获取执行后的目标
                goals = self._get_current_goals()
                proof_completed = goals is None  # 如果没有目标了，证明完成
                
                return CoqResponse(
                    success=True,
                    message=message,
                    goals=goals,
                    proof_completed=proof_completed
                )
            else:
                return CoqResponse(
                    success=False,
                    message=message,
                    error=error
                )
                
        except Exception as e:
            logger.error(f"Error executing tactic: {e}")
            return CoqResponse(
                success=False,
                message="",
                error=str(e)
            )

    def _format_coq_state_for_llm(self, goals: Optional[str], last_response: Optional[CoqResponse] = None) -> str:
        """为LLM格式化Coq状态信息"""
        state_parts = []
        
        if goals:
            state_parts.append(f"Current Goals:\n{goals}")
        else:
            state_parts.append("No current goals (proof may be complete)")
        
        if last_response and not last_response.success:
            state_parts.append(f"Last tactic failed: {last_response.error or last_response.message}")
        
        return '\n\n'.join(state_parts)

    def prove_theorem(self, theorem_statement: str) -> Dict[str, Any]:
        """
        证明定理的主要方法
        
        Args:
            theorem_statement: 定理陈述
            
        Returns:
            证明结果字典
        """
        logger.info(f"Starting proof of: {theorem_statement}")
        
        self.current_theorem = theorem_statement
        self.current_proof_status = ProofStatus.IN_PROGRESS
        self.proof_steps.clear()
        
        # 清空LLM上下文，开始新的证明
        self.llm.clear_context()
        
        step_count = 0
        last_response = None
        stuck_count = 0  # 连续失败计数
        
        while step_count < self.max_steps:
            step_count += 1
            
            # 获取当前目标
            current_goals = self._get_current_goals()
            
            # 检查是否完成
            if current_goals is None:
                self.current_proof_status = ProofStatus.COMPLETED
                logger.info("Proof completed successfully!")
                break
            
            # 为LLM准备输入
            coq_state = self._format_coq_state_for_llm(current_goals, last_response)
            
            if step_count == 1:
                llm_input = f"Prove this theorem: {theorem_statement}"
            else:
                llm_input = "What's the next tactic?"
            
            # 获取LLM建议的策略
            try:
                llm_output = self.llm.generate_with_context(
                    llm_input, 
                    coq_state=coq_state,
                    additional_context={"step": step_count, "theorem": theorem_statement}
                )
                
                tactic = self._extract_tactic_from_llm_output(llm_output)
                logger.info(f"Step {step_count}: LLM suggests tactic: {tactic}")
                
            except Exception as e:
                logger.error(f"Error getting LLM response: {e}")
                tactic = "assumption."
                llm_output = f"Error: {e}"
            
            # 执行策略
            coq_response = self._execute_tactic(tactic)
            
            # 记录步骤
            proof_step = ProofStep(
                step_number=step_count,
                tactic=tactic,
                coq_response=coq_response,
                timestamp=time.time(),
                llm_reasoning=llm_output
            )
            self.proof_steps.append(proof_step)
            
            if coq_response.success:
                stuck_count = 0  # 重置失败计数
                if coq_response.proof_completed:
                    self.current_proof_status = ProofStatus.COMPLETED
                    logger.info("Proof completed!")
                    break
            else:
                stuck_count += 1
                logger.warning(f"Tactic failed: {coq_response.error}")
                
                # 如果连续失败太多次，认为卡住了
                if stuck_count >= 5:
                    self.current_proof_status = ProofStatus.STUCK
                    logger.error("Proof appears to be stuck")
                    break
            
            last_response = coq_response
        
        # 如果达到最大步数仍未完成
        if step_count >= self.max_steps and self.current_proof_status == ProofStatus.IN_PROGRESS:
            self.current_proof_status = ProofStatus.FAILED
            logger.error("Proof failed: reached maximum steps")
        
        return self._generate_proof_summary()

    def _generate_proof_summary(self) -> Dict[str, Any]:
        """生成证明摘要"""
        successful_steps = [step for step in self.proof_steps if step.coq_response.success]
        
        return {
            "theorem": self.current_theorem,
            "status": self.current_proof_status.value,
            "total_steps": len(self.proof_steps),
            "successful_steps": len(successful_steps),
            "proof_script": [step.tactic for step in successful_steps],
            "execution_time": self.proof_steps[-1].timestamp - self.proof_steps[0].timestamp if self.proof_steps else 0,
            "steps_detail": [
                {
                    "step": step.step_number,
                    "tactic": step.tactic,
                    "success": step.coq_response.success,
                    "message": step.coq_response.message,
                    "error": step.coq_response.error
                }
                for step in self.proof_steps
            ]
        }

    def get_proof_script(self) -> str:
        """获取完整的证明脚本"""
        if self.current_proof_status != ProofStatus.COMPLETED:
            return "Proof not completed"
        
        successful_tactics = [step.tactic for step in self.proof_steps if step.coq_response.success]
        
        script_lines = ["Proof."]
        script_lines.extend(successful_tactics)
        script_lines.append("Qed.")
        
        return '\n'.join(script_lines)
