"""
整合的证明系统示例
展示如何使用上下文记忆的LLM和Coq交互Agent
"""

import argparse
from pathlib import Path
import json

import coq.coqtop as coqtop
import coq.runner as runner
from contextual_vllm import create_contextual_inference
from coq_llm_agent import CoqLLMAgent
from loguru import logger


class IntegratedProofSystem:
    """整合的证明系统"""
    
    def __init__(self, model_path: str, coq_project_path: str):
        self.model_path = model_path
        self.coq_project_path = Path(coq_project_path)
        
        # 初始化组件
        self._setup_llm()
        self._setup_coq()
        self._setup_agent()
    
    def _setup_llm(self):
        """设置LLM推理引擎"""
        logger.info("Setting up contextual LLM inference...")
        
        self.contextual_llm = create_contextual_inference(
            model_path=self.model_path,
            max_turns=15,  # 保留更多轮对话用于证明上下文
            max_context_length=4000,  # 更大的上下文窗口
            temperature=0.3,  # 较低的温度，更确定性的输出
            max_tokens=128,   # 策略通常较短
            num_return_sequences=1
        )
        
        logger.info("LLM inference engine ready")
    
    def _setup_coq(self):
        """设置Coq环境"""
        logger.info("Setting up Coq environment...")
        
        self.coqtop = coqtop.Coqtop()
        
        # 加载项目配置
        try:
            self.coqlib = runner.load_coq_project(self.coq_project_path)
            logger.info(f"Loaded Coq project configuration: {self.coqlib}")
        except Exception as e:
            logger.warning(f"Could not load Coq project config: {e}")
            self.coqlib = []
        
        logger.info("Coq environment ready")
    
    def _setup_agent(self):
        """设置LLM-Coq交互Agent"""
        logger.info("Setting up LLM-Coq interaction agent...")
        
        self.agent = CoqLLMAgent(
            contextual_inference=self.contextual_llm,
            coqtop_instance=self.coqtop,
            max_steps=100,  # 允许更多步骤
            timeout_per_step=30
        )
        
        logger.info("Agent ready")
    
    def start_coq_session(self, filename: str = None):
        """启动Coq会话"""
        logger.info("Starting Coq session...")
        
        try:
            version = self.coqtop.find_coq(None, None)
            logger.info(f"Using Coq version: {version}")
            
            [err, msg] = self.coqtop.start(
                filename=filename or str(self.coq_project_path),
                coqproject_args=self.coqlib,
                use_dune=False,
                dune_compile_deps=False,
                timeout=30,
                stderr_is_warning=True,
            )
            
            if err:
                raise Exception(f"Failed to start Coq: {err}")
            
            logger.info(f"Coq session started: {msg}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting Coq session: {e}")
            return False
    
    def prove_theorem(self, theorem_statement: str) -> dict:
        """证明单个定理"""
        logger.info(f"Attempting to prove: {theorem_statement}")
        
        # 首先发送定理陈述到Coq
        try:
            ok, msg, _, err = self.coqtop.advance(theorem_statement, in_script=True)
            if not ok:
                logger.error(f"Failed to start theorem: {err or msg}")
                return {
                    "success": False,
                    "error": f"Failed to start theorem: {err or msg}",
                    "theorem": theorem_statement
                }
        except Exception as e:
            logger.error(f"Error sending theorem to Coq: {e}")
            return {
                "success": False,
                "error": str(e),
                "theorem": theorem_statement
            }
        
        # 使用Agent进行证明
        result = self.agent.prove_theorem(theorem_statement)
        
        # 如果证明成功，发送Qed
        if result["status"] == "completed":
            try:
                ok, msg, _, err = self.coqtop.advance("Qed.", in_script=True)
                if ok:
                    logger.info("Theorem proved and closed with Qed.")
                else:
                    logger.warning(f"Could not close with Qed: {err or msg}")
            except Exception as e:
                logger.warning(f"Error closing theorem: {e}")
        
        return result
    
    def interactive_mode(self):
        """交互模式"""
        print("=== Interactive Proof System ===")
        print("Enter Coq theorem statements to prove.")
        print("Commands:")
        print("  'quit' or 'exit' - Exit the system")
        print("  'clear' - Clear conversation context")
        print("  'status' - Show system status")
        print("  'save <filename>' - Save conversation history")
        print()
        
        while True:
            try:
                user_input = input("Theorem> ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                elif user_input.lower() == 'clear':
                    self.contextual_llm.clear_context()
                    print("Conversation context cleared.")
                    continue
                
                elif user_input.lower() == 'status':
                    summary = self.contextual_llm.get_conversation_summary()
                    print(f"Conversation turns: {summary['total_turns']}")
                    print(f"Context config: {summary['context_config']}")
                    continue
                
                elif user_input.lower().startswith('save '):
                    filename = user_input[5:].strip()
                    if filename:
                        self.contextual_llm.export_conversation(filename)
                        print(f"Conversation saved to {filename}")
                    else:
                        print("Please specify a filename")
                    continue
                
                elif not user_input:
                    continue
                
                # 尝试证明定理
                result = self.prove_theorem(user_input)
                
                print(f"\nResult: {result['status'].upper()}")
                print(f"Steps: {result['successful_steps']}/{result['total_steps']}")
                
                if result['status'] == 'completed':
                    print("✓ Proof completed successfully!")
                    print("\nProof script:")
                    print("Proof.")
                    for tactic in result['proof_script']:
                        print(f"  {tactic}")
                    print("Qed.")
                
                elif result['status'] == 'failed':
                    print("✗ Proof failed")
                    if 'error' in result:
                        print(f"Error: {result['error']}")
                
                elif result['status'] == 'stuck':
                    print("⚠ Proof appears to be stuck")
                    print("Last few steps:")
                    for step in result['steps_detail'][-3:]:
                        status = "✓" if step['success'] else "✗"
                        print(f"  {status} {step['tactic']}")
                        if not step['success'] and step['error']:
                            print(f"    Error: {step['error']}")
                
                print("-" * 60)
                
            except KeyboardInterrupt:
                print("\nExiting...")
                break
            except Exception as e:
                logger.error(f"Error in interactive mode: {e}")
                print(f"Error: {e}")
    
    def batch_prove(self, theorems: list, output_file: str = None):
        """批量证明定理"""
        results = []
        
        for i, theorem in enumerate(theorems, 1):
            logger.info(f"Proving theorem {i}/{len(theorems)}: {theorem}")
            
            result = self.prove_theorem(theorem)
            results.append(result)
            
            print(f"[{i}/{len(theorems)}] {theorem}")
            print(f"  Status: {result['status']}")
            print(f"  Steps: {result['successful_steps']}/{result['total_steps']}")
            
            if result['status'] == 'completed':
                print("  ✓ Success")
            else:
                print("  ✗ Failed")
            print()
        
        # 保存结果
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            logger.info(f"Results saved to {output_file}")
        
        # 统计
        successful = sum(1 for r in results if r['status'] == 'completed')
        print(f"\nSummary: {successful}/{len(theorems)} theorems proved successfully")
        
        return results


def main():
    parser = argparse.ArgumentParser(description="Integrated Proof System")
    
    parser.add_argument("--model", "-m", required=True, help="Path to the language model")
    parser.add_argument("--project", "-p", required=True, help="Path to the Coq project")
    parser.add_argument("--interactive", "-i", action="store_true", help="Run in interactive mode")
    parser.add_argument("--theorem", "-t", help="Single theorem to prove")
    parser.add_argument("--batch", "-b", help="File containing theorems to prove (one per line)")
    parser.add_argument("--output", "-o", help="Output file for results")
    
    args = parser.parse_args()
    
    # 初始化系统
    system = IntegratedProofSystem(args.model, args.project)
    
    # 启动Coq会话
    if not system.start_coq_session():
        print("Failed to start Coq session")
        return 1
    
    try:
        if args.interactive:
            system.interactive_mode()
        
        elif args.theorem:
            result = system.prove_theorem(args.theorem)
            print(json.dumps(result, indent=2))
        
        elif args.batch:
            with open(args.batch, 'r', encoding='utf-8') as f:
                theorems = [line.strip() for line in f if line.strip()]
            
            system.batch_prove(theorems, args.output)
        
        else:
            print("Please specify --interactive, --theorem, or --batch mode")
            return 1
    
    except Exception as e:
        logger.error(f"Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())
