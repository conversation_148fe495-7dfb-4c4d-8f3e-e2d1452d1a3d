#!/usr/bin/env python3
"""
Simple test script for vLLM inference.
"""

import logging
import argparse
from pathlib import Path
from typing import List, Optional

from vllmInference import (
    VLLMInference,
    ModelConfig,
    GenerationConfig,
    create_model_config,
    create_generation_config
)

def setup_logging(verbose: bool = False):
    """Set up logging configuration"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_generation(model_path: str, prompts: List[str], 
                   temperature: float = 0.7, max_tokens: int = 256,
                   num_samples: int = 1, verbose: bool = False):
    """Test text generation with vLLM"""
    setup_logging(verbose)
    logger = logging.getLogger(__name__)
    
    try:
        # Create configurations
        logger.info(f"Creating model config with path: {model_path}")
        model_config = create_model_config(
            model_path=model_path,
            tensor_parallel_size=1,
            gpu_memory_utilization=0.8
        )
        
        logger.info(f"Creating generation config with temp={temperature}, max_tokens={max_tokens}")
        generation_config = create_generation_config(
            temperature=temperature,
            max_tokens=max_tokens,
            num_return_sequences=num_samples
        )
        
        # Initialize inference engine
        logger.info("Initializing VLLMInference...")
        inference = VLLMInference(model_config, generation_config)
        logger.info("VLLMInference initialized successfully")
        
        # Generate for each prompt
        for i, prompt in enumerate(prompts, 1):
            logger.info(f"Generating for prompt {i}/{len(prompts)}")
            print(f"\nPrompt {i}: {prompt}")
            
            results = inference.generate(prompt)
            
            print(f"\nGenerated {len(results)} response(s):")
            for j, result in enumerate(results, 1):
                print(f"\n--- Response {j} ---")
                print(result)
                print("-" * 40)
        
        return True
        
    except Exception as e:
        logger.error(f"Error during generation: {e}", exc_info=True)
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test vLLM inference")
    
    parser.add_argument(
        "--model", "-m", type=str, required=True,
        help="Path to the model"
    )
    
    parser.add_argument(
        "--prompt", "-p", type=str, action="append", default=[],
        help="Prompt for generation (can be specified multiple times)"
    )
    
    parser.add_argument(
        "--prompt-file", "-f", type=str,
        help="File containing prompts (one per line)"
    )
    
    parser.add_argument(
        "--temperature", "-t", type=float, default=0.7,
        help="Temperature for sampling (default: 0.7)"
    )
    
    parser.add_argument(
        "--max-tokens", "-mt", type=int, default=256,
        help="Maximum number of tokens to generate (default: 256)"
    )
    
    parser.add_argument(
        "--num-samples", "-n", type=int, default=1,
        help="Number of samples to generate (default: 1)"
    )
    
    parser.add_argument(
        "--verbose", "-v", action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Collect prompts
    prompts = list(args.prompt)
    
    if args.prompt_file:
        try:
            with open(args.prompt_file, 'r', encoding='utf-8') as f:
                file_prompts = [line.strip() for line in f if line.strip()]
                prompts.extend(file_prompts)
        except Exception as e:
            print(f"Error reading prompt file: {e}")
            return 1
    
    if not prompts:
        # Default prompts if none provided
        prompts = [
            """Output only valid Coq proof script. Do not explain or comment. No markdown. No text. Just code.
        Example 1:
        <Input>: Theorem add_O_n : forall n : nat, 0 + n = n.
        <Output>: Proof.
        intros n.
        simpl.
        reflexivity.
        Qed.

        Example 2:
        <Input>: Theorem mult_0_r : forall n : nat, n * 0 = 0.
        <Output>: Proof.
        intros n.
        induction n as [| n' IH].
        - simpl. reflexivity.
        - simpl. rewrite IH. reflexivity.
        Qed.

        Task 1:
        <Input>: Theorem plus_n_Sm : forall n m : nat, S (n + m) = n + S m.
        Proof.
        """]

    # Run test
    success = test_generation(
        model_path=args.model,
        prompts=prompts,
        temperature=args.temperature,
        max_tokens=args.max_tokens,
        num_samples=args.num_samples,
        verbose=args.verbose
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
