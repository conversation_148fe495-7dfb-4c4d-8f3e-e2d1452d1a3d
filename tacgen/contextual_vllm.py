"""
带上下文记忆的vLLM推理引擎
支持流式对话和上下文管理
"""

import logging
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass, field
from collections import deque
import json
import time

from tacgen.vllmInference import VLLMInference, ModelConfig, GenerationConfig
from loguru import logger


@dataclass
class ConversationTurn:
    """单轮对话记录"""
    timestamp: float
    user_input: str
    assistant_output: str
    context_info: Optional[Dict[str, Any]] = None  # 额外的上下文信息，如Coq状态
    

@dataclass
class ContextConfig:
    """上下文管理配置"""
    max_turns: int = 10  # 最大保留对话轮数
    max_context_length: int = 3000  # 最大上下文长度（token估算）
    include_system_prompt: bool = True
    context_compression: bool = True  # 是否启用上下文压缩


class ContextualVLLMInference:
    """
    带上下文记忆的vLLM推理引擎
    """
    
    def __init__(self, 
                 model_config: ModelConfig, 
                 generation_config: GenerationConfig,
                 context_config: ContextConfig = None):
        
        self.vllm_inference = VLLMInference(model_config, generation_config)
        self.context_config = context_config or ContextConfig()
        
        # 对话历史存储
        self.conversation_history: deque[ConversationTurn] = deque(
            maxlen=self.context_config.max_turns
        )
        
        # 系统提示词
        self.system_prompt = self._get_default_system_prompt()
        
        logger.info("ContextualVLLMInference initialized")
    
    def _get_default_system_prompt(self) -> str:
        """获取默认系统提示词"""
        return """You are an expert Coq theorem prover assistant. Your task is to help prove theorems step by step.

Guidelines:
1. Generate valid Coq tactics only
2. Consider the current proof state and goals
3. Use appropriate tactics for the situation
4. Be concise and precise
5. If stuck, try alternative approaches

Remember the conversation context and build upon previous interactions."""

    def set_system_prompt(self, prompt: str):
        """设置系统提示词"""
        self.system_prompt = prompt
        logger.info("System prompt updated")

    def _estimate_token_count(self, text: str) -> int:
        """估算文本的token数量（简单估算）"""
        # 简单估算：1个token约等于4个字符
        return len(text) // 4

    def _build_context_prompt(self, current_input: str, coq_state: Optional[str] = None) -> str:
        """构建包含上下文的完整提示"""
        
        prompt_parts = []
        
        # 添加系统提示
        if self.context_config.include_system_prompt:
            prompt_parts.append(f"System: {self.system_prompt}")
        
        # 添加对话历史
        total_length = 0
        history_parts = []
        
        # 从最新的对话开始，向前添加
        for turn in reversed(self.conversation_history):
            turn_text = f"User: {turn.user_input}\nAssistant: {turn.assistant_output}"
            
            # 如果有Coq状态信息，也包含进去
            if turn.context_info and 'coq_state' in turn.context_info:
                turn_text += f"\nCoq State: {turn.context_info['coq_state']}"
            
            turn_length = self._estimate_token_count(turn_text)
            
            if total_length + turn_length > self.context_config.max_context_length:
                break
                
            history_parts.insert(0, turn_text)
            total_length += turn_length
        
        if history_parts:
            prompt_parts.append("Previous conversation:")
            prompt_parts.extend(history_parts)
        
        # 添加当前Coq状态（如果有）
        if coq_state:
            prompt_parts.append(f"Current Coq State:\n{coq_state}")
        
        # 添加当前用户输入
        prompt_parts.append(f"User: {current_input}")
        prompt_parts.append("Assistant:")
        
        return "\n\n".join(prompt_parts)

    def _compress_context(self):
        """压缩上下文（简单实现）"""
        if not self.context_config.context_compression:
            return
            
        if len(self.conversation_history) >= self.context_config.max_turns:
            # 简单策略：保留最近的几轮对话，压缩较早的对话
            # 这里可以实现更复杂的压缩逻辑
            logger.info("Context compression triggered")

    def generate_with_context(self, 
                            user_input: str, 
                            coq_state: Optional[str] = None,
                            additional_context: Optional[Dict[str, Any]] = None) -> str:
        """
        带上下文的生成
        
        Args:
            user_input: 用户输入
            coq_state: 当前Coq状态
            additional_context: 额外的上下文信息
            
        Returns:
            生成的回复
        """
        
        # 构建完整提示
        full_prompt = self._build_context_prompt(user_input, coq_state)
        
        logger.debug(f"Full prompt length: {len(full_prompt)}")
        
        # 生成回复
        results = self.vllm_inference.generate(full_prompt)
        
        if not results:
            assistant_output = "I apologize, but I couldn't generate a response."
        else:
            assistant_output = results[0]  # 取第一个结果
        
        # 记录对话
        context_info = {}
        if coq_state:
            context_info['coq_state'] = coq_state
        if additional_context:
            context_info.update(additional_context)
        
        turn = ConversationTurn(
            timestamp=time.time(),
            user_input=user_input,
            assistant_output=assistant_output,
            context_info=context_info if context_info else None
        )
        
        self.conversation_history.append(turn)
        
        # 检查是否需要压缩上下文
        self._compress_context()
        
        return assistant_output

    def clear_context(self):
        """清空对话上下文"""
        self.conversation_history.clear()
        logger.info("Conversation context cleared")

    def get_conversation_summary(self) -> Dict[str, Any]:
        """获取对话摘要"""
        return {
            "total_turns": len(self.conversation_history),
            "oldest_timestamp": self.conversation_history[0].timestamp if self.conversation_history else None,
            "newest_timestamp": self.conversation_history[-1].timestamp if self.conversation_history else None,
            "context_config": {
                "max_turns": self.context_config.max_turns,
                "max_context_length": self.context_config.max_context_length
            }
        }

    def export_conversation(self, filepath: str):
        """导出对话历史"""
        conversation_data = []
        for turn in self.conversation_history:
            conversation_data.append({
                "timestamp": turn.timestamp,
                "user_input": turn.user_input,
                "assistant_output": turn.assistant_output,
                "context_info": turn.context_info
            })
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(conversation_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Conversation exported to {filepath}")

    def load_conversation(self, filepath: str):
        """加载对话历史"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                conversation_data = json.load(f)
            
            self.conversation_history.clear()
            for turn_data in conversation_data:
                turn = ConversationTurn(
                    timestamp=turn_data["timestamp"],
                    user_input=turn_data["user_input"],
                    assistant_output=turn_data["assistant_output"],
                    context_info=turn_data.get("context_info")
                )
                self.conversation_history.append(turn)
            
            logger.info(f"Conversation loaded from {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to load conversation: {e}")


def create_contextual_inference(model_path: str, 
                               max_turns: int = 10,
                               max_context_length: int = 3000,
                               **generation_kwargs) -> ContextualVLLMInference:
    """
    创建带上下文的推理引擎的便捷函数
    """
    from vllmInference import create_model_config, create_generation_config
    
    model_config = create_model_config(model_path)
    generation_config = create_generation_config(**generation_kwargs)
    context_config = ContextConfig(
        max_turns=max_turns,
        max_context_length=max_context_length
    )
    
    return ContextualVLLMInference(model_config, generation_config, context_config)


# 使用示例
if __name__ == "__main__":
    # 创建带上下文的推理引擎
    contextual_inference = create_contextual_inference(
        model_path="/path/to/your/model",
        temperature=0.7,
        max_tokens=256
    )
    
    # 模拟对话
    response1 = contextual_inference.generate_with_context(
        "I want to prove that 0 + n = n for all natural numbers n.",
        coq_state="Goal: forall n : nat, 0 + n = n"
    )
    print("Response 1:", response1)
    
    response2 = contextual_inference.generate_with_context(
        "What's the first step?",
        coq_state="Goal: forall n : nat, 0 + n = n"
    )
    print("Response 2:", response2)
    
    # 查看对话摘要
    summary = contextual_inference.get_conversation_summary()
    print("Conversation summary:", summary)
