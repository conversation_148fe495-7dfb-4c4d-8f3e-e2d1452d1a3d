{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ed24de93", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job517to530.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present7.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present9.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job554to562.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task486to506.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/matte.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/cube.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job311to314.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job490to494.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job507to510.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job283to286.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/kempe.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/fourcolor.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task507to541.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/gtree.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job439to465.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job271to278.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task303to322.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job001to106.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/contract.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present5.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/initgtree.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job299to302.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/ctreerestrict.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task589to633.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job307to310.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/hubcap.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job486to489.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/chromogram.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job207to214.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/cfquiz.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/snip.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task215to234.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job503to506.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/initctree.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job563to588.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job223to226.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job315to318.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job399to438.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present6.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job279to282.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/jordan.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job295to298.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job542to545.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present11.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/configurations.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present8.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/geometry.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/discharge.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job190to206.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/gridmap.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/cfmap.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task235to282.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job254to270.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/finitize.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/color.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job589to610.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/embed.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/redpart.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job287to290.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job611to617.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job215to218.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job535to541.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job466to485.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/ctree.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/cfcontract.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/gtreerestrict.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job319to322.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/hypermap.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/walkup.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job550to553.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/reducibility.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/realplane.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task283to302.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/patch.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task001to214.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/quiztree.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job219to222.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/kempetree.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job323to383.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job231to234.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job107to164.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/approx.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/combinatorial4ct.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job291to294.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/grid.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job546to549.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/present10.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/unavoidability.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job165to189.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job499to502.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/dedekind.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/sew.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/quiz.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job495to498.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/cfcolor.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job384to398.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/discretize.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job239to253.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job235to238.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job618to622.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job303to306.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task323to485.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/cfreducible.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/birkhoff.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job623to633.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job227to230.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/dyck.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job531to534.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/job511to516.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/revsnip.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/task542to588.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/part.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/proof/coloring.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/reals/realsyntax.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/reals/real.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/reals/realcategorical.v', '/media/llm4itp/ssd1/ytchen/coq-python/fourcolor-master/theories/reals/realprop.v'])"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import json \n", "with open ('premises.json', 'r') as f:\n", "    data = json.load(f)\n", "data.keys()"]}, {"cell_type": "code", "execution_count": 4, "id": "8bc2340a", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Set': 'Set Implicit Arguments.'},\n", " {'Unset': 'Unset Strict Implicit.'},\n", " {'Unset': 'Unset Printing Implicit Defensive.'},\n", " {'Section': 'Section Embeddings.'},\n", " {'Variable': 'Variables (Gm Gc : hypermap) (rc cc : seq Gc) (h : Gc -> Gm).'},\n", " {'Let': 'Let rrc := rev rc.'},\n", " {'Notation': 'Notation ac := (kernel rc).'},\n", " {'Notation': 'Notation bc := [predC rc].'},\n", " {'Let': 'Let ac_bc : {subset ac <= bc} := subsetP (kernel_off_ring rc).'},\n", " {'Let': 'Let acF := kernelF rc.'},\n", " {'Hypothesis': 'Hypothesis cexGm : minimal_counter_example Gm.'},\n", " {'Hypothesis': 'Hypothesis embedGc : embeddable rc.'},\n", " {'Hypothesis': 'Hypothesis embed_h : preembedding ac h.'},\n", " {'Hypothesis': 'Hypothesis Cred_cc : C_reducible rc cc.'},\n", " {'Let': 'Let planarGm : planar Gm := cexGm.'},\n", " {'Let': \"Let bridge'Gm := bridgeless_cface cexGm.\"},\n", " {'Let': 'Let plainGm : plain Gm := cexGm.'},\n", " {'Let': 'Let e2m := plain_eq cexGm.'},\n", " {'Let': 'Let n3m := cubic_eq cexGm.'},\n", " {'Let': 'Let pentaGm : pentagonal Gm := cexGm.'},\n", " {'Let': 'Let geoGm : planar_bridgeless_plain_connected Gm := cexGm.'},\n", " {'Let': 'Let planarGc : planar Gc := embedGc.'},\n", " {'Let': \"Let bridge'Gc := bridgeless_cface embedGc.\"},\n", " {'Let': 'Let plainGc : plain Gc := embedGc.'},\n", " {'Let': 'Let cubicGc : quasicubic rc := embedGc.'},\n", " {'Let': 'Let e2c := plain_eq embedGc.'},\n", " {'Let': 'Let n3c := quasicubic_eq cubicGc.'},\n", " {'Let': 'Let geoGc : planar_bridgeless_plain_connected Gc := embedGc.'},\n", " {'Let': 'Let UNrc : sfcycle node rc := embedGc.'},\n", " {'Let': 'Let cycNrc := scycle_cycle UNrc.'},\n", " {'Let': 'Let Urc := scycle_uniq UNrc.'},\n", " {'Let': 'Let UrcF := scycle_simple UNrc.'},\n", " {'Notation': 'Notation hc := (edge_central h).'},\n", " {'Let': 'Let hcE : {mono edge : x / x \\\\in hc}.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let hF := preembedding_face embed_h.'},\n", " {'Notation': 'Notation h_spoke := ((preembedding_simple_path embed_h acF) _ _).'},\n", " {'Lemma': ['Lemma embed_functor x : x \\\\in ac -> edge x \\\\in ac -> h (edge x) = edge (h x).',\n", "   'Proof.',\n", "   'suffices{x}: ~ exists2 x, x \\\\in [predD ac & hc] & exists2 p, scycle rlink (x :: p) & {subset p <= [predI ac & hc]}.',\n", "   \"move=> IH ac_x ac_ex; apply/eqP/idPn=> eh'hex; case: IH.\",\n", "   \"exists x => [|{eh'hex}]; first by rewrite !inE eh'hex.\",\n", "   'case: {ac_ex ac_x}(h_spoke ac_ex ac_x) => p []; case/lastP: p => // p y xRp.',\n", "   'rewrite last_rcons all_rcons => yFx _ [Up /andP[_ /allP ac_p]].',\n", "   'exists p => //; rewrite /scycle /= rcons_path -(rlinkFr yFx) -rcons_path xRp.',\n", "   'by rewrite simple_cons -(closed_connect (fbandF p) yFx) -simple_rcons.',\n", "   'suffices: ~ exists2 x, x \\\\notin hc & exists2 p, scycle rlink (x :: p) & {subset p <= hc} /\\\\ {subset diskN (x :: p) <= ac}.',\n", "   \"move=> IH [x /andP[/= eh'hex ac_x] [p URp hac_p]].\",\n", "   'have ac_xp: {subset (x :: p) <= ac} by move=> y /predU1P[-> | /hac_p/andP[]].',\n", "   \"have [dF'rc|] := boolP (all [predC diskF (x :: p)] rc).\",\n", "   'case: IH; exists x => //; exists p => //.',\n", "   'split=> [y | y dNy]; first by case/hac_p/andP.',\n", "   'case xpFy: (y \\\\in fband (x :: p)).',\n", "   'case/(@hasP _ _ (x :: p)): xpFy => z pz yFz.',\n", "   'by rewrite (closed_connect acF yFz) ac_xp.',\n", "   \"apply: contraL dF'rc => /hasP[z rc_z yFz].\",\n", "   'rewrite all_predC negbK; apply/hasP; exists z => //=.',\n", "   'by rewrite -(closed_connect (diskF_face _) yFz) !inE xpFy.',\n", "   'have rr_xp1: rev_ring (rot 1 (x :: p)) = edge x :: rev_ring p.',\n", "   'by rewrite {1}/rev_ring map_rot /= rot1_cons rev_rcons.',\n", "   'have URp1: scycle rlink (rot 1 (x :: p)) by rewrite rot_scycle.',\n", "   \"rewrite all_predC negbK => /= dF'rc.\",\n", "   'case: IH; exists (edge x); first by rewrite hcE.',\n", "   'exists (rev_ring p); first by rewrite -rr_xp1; apply: scycle_rev_ring.',\n", "   'split=> y; first by rewrite -hcE /= mem_rev_ring // => /hac_p/andP[].',\n", "   'have proper_xp1: proper_ring (rot 1 (x :: p)).',\n", "   'rewrite proper_ring_rot //; case: (p) URp hac_p => [|x2 []] //=.',\n", "   \"by rewrite /scycle /= /rlink cfaceC bridge'Gc.\",\n", "   'move=> _ /allP/=/andP[/andP[_ hcx2] _]; apply: contraTneq hcx2 => <-.',\n", "   'by rewrite /= hcE.',\n", "   \"rewrite /= -rr_xp1 diskN_rev_ring // inE => /= dNr'y.\",\n", "   'apply/hasP => [] [z rcz yFz].',\n", "   'have: z \\\\in diskF (x :: p).',\n", "   'apply/andP; split.',\n", "   'apply/hasP=> [] [t rct tFz]; case/ac_xp/hasP: rct.',\n", "   'by exists z; rewrite // cfaceC.',\n", "   \"case/hasP: dF'rc => t; rewrite -(fconnect_cycle cycNrc rcz) => zNt /=.\",\n", "   'by rewrite (closed_connect (diskN_node _) zNt) => /andP[].',\n", "   'rewrite -(closed_connect (diskF_face _) yFz) => /andP[_].',\n", "   'rewrite /= unfold_in => /hasP[y1 xpy1 yDy1].',\n", "   \"case/hasP: dNr'y; exists y1; first by rewrite mem_rot.\",\n", "   'apply: etrans yDy1; apply: {y1 xpy1}eq_connect => y1 y2.',\n", "   'by rewrite /dlink /= mem_rot.',\n", "   \"case=> x hc'x [p URp []]; move: {2}_.+1 (ltnSn #|diskN (x :: p)|) URp => n.\",\n", "   \"elim: n => // n IHn in x hc'x p *; rewrite ltnS => le_dN_n URp p_hc dN_ac.\",\n", "   'set xp := x :: p in le_dN_n URp p_hc dN_ac.',\n", "   'have dNx: x \\\\in diskN xp by rewrite diskN_E mem_head.',\n", "   'have dNnx: node x \\\\in diskN xp by rewrite -(fclosed1 (diskN_node xp)).',\n", "   'have dNnnx: node (node x) \\\\in diskN xp by rewrite -(fclosed1 (diskN_node xp)).',\n", "   'have [cycRp UpF] := andP URp; have Up := simple_uniq UpF; rewrite /xp in cycRp.',\n", "   \"case Dp0: p (cycRp) => [|x0 p0]; first by rewrite /= /rlink cfaceC bridge'Gc.\",\n", "   'clear 1; pose enx := edge (node x); pose p1 := rcons p enx.',\n", "   'have xRp1: path rlink x p1.',\n", "   'by rewrite rcons_path /= {2}/rlink cface1r /enx nodeK -rcons_path.',\n", "   'have{UpF} Up1: simple p1.',\n", "   'have enxFx: cface enx x by rewrite cface1 nodeK.',\n", "   'by rewrite /p1 simple_rcons (closed_connect (fbandF p) enxFx) -simple_cons.',\n", "   \"have [n3x x'nx] := cubicP cubicGc _ (ac_bc (dN_ac x dNx)).\",\n", "   'case hc_nx: (node x \\\\in hc).',\n", "   'case hc_nnx: (node (node x) \\\\in hc).',\n", "   \"case/eqP: hc'x; apply: faceI; rewrite -(canRL nodeK (n3m _)).\",\n", "   'do 2!apply: (canRL (canF_sym nodeK)); rewrite -{1}n3x -hF; last first.',\n", "   'by rewrite inE /= (fclosed1 (fbandF rc)) nodeK [~~ _]dN_ac.',\n", "   'rewrite nodeK -(eqP hc_nnx) -hF; last first.',\n", "   'by rewrite inE /= (fclosed1 (fbandF rc)) nodeK [~~ _]dN_ac.',\n", "   'rewrite nodeK -(eqP hc_nx) -hF ?nodeK //.',\n", "   'by rewrite inE /= (fclosed1 (fbandF rc)) nodeK [~~ _]dN_ac.',\n", "   'pose ennx := edge (node (node x)).',\n", "   'pose i := find (cface ennx) p1; pose q := ennx :: take i p1.',\n", "   'have le_i_p1: i <= size p1 by apply: find_size.',\n", "   'have URq: scycle rlink q.',\n", "   'have{xRp1} ennxRp1: path rlink ennx p1.',\n", "   'move: xRp1; rewrite -(drop0 p1) (drop_nth x) ?nth0 ?size_rcons //=.',\n", "   'by rewrite {3}/rlink /= e2c (canRL nodeK n3x) -cface1.',\n", "   'apply/andP; move: le_i_p1 ennxRp1 Up1; rewrite leq_eqVlt.',\n", "   'case/predU1P=> [eq_i_p1 | lt_i_p1].',\n", "   'rewrite /q eq_i_p1 take_size /= rcons_path simple_cons => -> ->.',\n", "   'rewrite last_rcons /rlink /= cface1r nodeK e2c [_ \\\\in _]has_find -/i.',\n", "   'by rewrite eq_i_p1 ltnn.',\n", "   'rewrite -(cat_take_drop i.+1 p1) (take_nth x) //= cat_path last_rcons.',\n", "   'have ennxFi: cface ennx (nth x p1 i) by apply: nth_find; rewrite has_find.',\n", "   'rewrite !rcons_path -(rlinkFr ennxFi) simple_cat => /andP[-> _] /andP[].',\n", "   'rewrite -rot1_cons simple_rot !simple_cons.',\n", "   'by rewrite -(closed_connect (fbandF _) ennxFi).',\n", "   'have p1_hc: {subset take i p1 <= hc}.',\n", "   'move=> y /mem_take; rewrite mem_rcons => /predU1P[-> | /p_hc//].',\n", "   'by rewrite hcE.',\n", "   \"have dNq'x: (x \\\\in diskN q) = false.\",\n", "   'rewrite 2!(fclosed1 (diskN_node q)) -[node _]e2c.',\n", "   'apply: (diskN_edge_ring geoGc URq _ (mem_head _ _)).',\n", "   'rewrite /q; case Dp2: (take i p1) => [|x1 [|x2 p2]] //.',\n", "   \"by rewrite /scycle /q Dp2 /= /rlink cfaceC bridge'Gc in URq.\",\n", "   'apply: contraFN hc_nnx; rewrite e2c => /eqP->.',\n", "   'by rewrite p1_hc //= Dp2 ?mem_head.',\n", "   'suffices dNq_dN: {subset diskN q <= diskN xp}.',\n", "   \"have{hc_nnx} hc'ennx: ennx \\\\notin hc by rewrite hcE hc_nnx.\",\n", "   \"case: {IHn}(IHn _ hc'ennx (take i p1)) => // [|_ /dNq_dN/dN_ac//].\",\n", "   'apply: leq_trans le_dN_n; rewrite -/q [rhs in _ < rhs](cardD1 x) dNx ltnS.',\n", "   'apply/subset_leq_card/subsetP=> y dNq_y; rewrite !inE dNq_dN // andbT.',\n", "   \"by apply: contraFN dNq'x => /eqP <-.\",\n", "   'move=> t; rewrite unfold_in => /hasP[z q_z /connectP[r yDr ->{t}]].',\n", "   'move: yDr; set y := finv node z.',\n", "   'have: y \\\\in diskN xp /\\\\ y \\\\in diskN q.',\n", "   'rewrite !(fclosed1 (diskN_node _) y)(f_finv nodeI) !diskN_E /=.',\n", "   'rewrite [diskE]lock [q]lock !inE -!lock q_z -orbA; split=> //=.',\n", "   \"have [_|p'z /=] := boolP (z \\\\in p); first exact: orbT.\",\n", "   'move: q_z; rewrite inE /p1 -cats1 take_cat // 2!fun_if mem_cat.',\n", "   'case/predU1P=> [-> | ].',\n", "   'rewrite -(fclosed1 (diskE_edge planarGc URp)) inE /= dNnnx orbC.',\n", "   'rewrite (contraFN _ hc_nnx) // inE -(inj_eq nodeI) n3x.',\n", "   \"by rewrite eq_sym (negPf x'nx) => /p_hc.\",\n", "   \"rewrite (contraNF (@mem_take i _ p z) p'z) (negPf p'z).\",\n", "   'rewrite ltn_neqAle; case: eqP => [-> | _ /=]; first by rewrite subnn.',\n", "   \"rewrite -ltnS -(size_rcons p enx) -has_find; case: ifPn => // p1F'ennx.\",\n", "   'case: (i - _) => //= _; rewrite inE => /eqP Dz.',\n", "   'rewrite Dz -(fclosed1 (diskE_edge planarGc URp)) inE orbC /= dNnx.',\n", "   \"rewrite inE (negPf x'nx) (contra _ p1F'ennx) // => pnx.\",\n", "   'apply/hasP; exists (node x); first by rewrite mem_rcons mem_behead.',\n", "   'by rewrite cface1 nodeK.',\n", "   'elim: {z q_z}r y => [|z r IHr] y [dNy dNq_y] //=.',\n", "   \"rewrite {1}/dlink /= -andbA => /and3P[q'y yCz]; apply: {r}IHr.\",\n", "   'rewrite !(fclosed1 (diskN_node _) z).',\n", "   'have{z yCz} [<- // | <-] := clinkP yCz.',\n", "   'rewrite !diskN_E !(fclosed1 (diskE_edge _ _) (node _)) // !faceK.',\n", "   \"rewrite ![y \\\\in diskE _]inE /= q'y {}dNy dNq_y orbT andbT; split=> //.\",\n", "   \"apply/orP; right; rewrite inE; case: eqP (dNq'x) => [<- /idP// | _ _] /=.\",\n", "   \"apply: contra q'y => py; rewrite !inE -[p1]cats1 take_cat orbC {le_i_p1}.\",\n", "   'case: ifP => lt_i_p; last by rewrite mem_cat py.',\n", "   'without loss{py}: / y \\\\in drop i p.',\n", "   'by move: py; rewrite -{1}(cat_take_drop i p) mem_cat => /orP[] -> // ->.',\n", "   \"rewrite (drop_nth x) // => p_y; case/idP: dNq'x.\",\n", "   'move: (mem_rot 1 xp) cycRp Up; rewrite -(rot_uniq 1) rot1_cons /=.',\n", "   'rewrite -(cat_take_drop i.+1 p) rcons_cat.',\n", "   'case/splitPl: p_y => r0 r Lr0; rewrite rcons_cat cat_uniq catA.',\n", "   'rewrite -[x in x \\\\in _](last_rcons y r); move: {r}(rcons r x) => r.',\n", "   'rewrite has_cat cat_path last_cat (take_nth x) // last_rcons {}Lr0.',\n", "   'move=> Dxp /andP[_ yRr] /and3P[_ /norP[_ Ur] _].',\n", "   'have{r0 Dxp Ur}: ~~ has (mem (fband q)) r.',\n", "   'apply: contra Ur => /hasP[z rz /= qFz]; apply/hasP; exists z => //=.',\n", "   'have /hasP[t p_t zFt]: z \\\\in fband (rcons (take i p) (nth x p i)).',\n", "   'move: qFz; rewrite -rot1_cons fband_rot !fband_cons -[p1]cats1 take_cat.',\n", "   'rewrite lt_i_p; congr (_ || _); apply: (same_connect_r cfaceC).',\n", "   'suffices: cface ennx (nth x p1 i) by rewrite nth_rcons lt_i_p.',\n", "   'by apply: nth_find; rewrite has_find size_rcons leqW.',\n", "   'by rewrite (scycle_cface URp _ _ zFt) // -Dxp !mem_cat (p_t, rz) ?orbT.',\n", "   \"elim: r y dNq_y yRr => //= z r IHr y dNq_y /andP[yRz rRr] /norP[qF'z qF'r].\",\n", "   'suffices: z \\\\in diskF q by case/andP=> _ /IHr->.',\n", "   'rewrite /rlink cface1 in yRz; rewrite -(closed_connect (diskF_face q) yRz).',\n", "   \"rewrite inE /= (closed_connect (fbandF q) yRz) qF'z.\",\n", "   'by rewrite (fclosed1 (diskN_node q)) edgeK.',\n", "   'have dNenx: enx \\\\in diskN xp.',\n", "   'rewrite diskN_E inE orbC /= -(fclosed1 (diskE_edge _ _)) // inE /= dNnx.',\n", "   \"by rewrite (contraFN _ hc_nx) // inE (negPf x'nx); apply: p_hc.\",\n", "   'have ac_eenx: edge enx \\\\in ac by rewrite e2c dN_ac.',\n", "   'have [q1 [enxRq1 Lq1 ntq1] [Uq1 q1_ac]] := h_spoke ac_eenx (dN_ac _ dNenx).',\n", "   'pose i := find (mem (fband p1)) q1; pose q2 := take i q1.',\n", "   'have Upq2: ~~ has (mem (fband q2)) p1.',\n", "   'apply/hasP=> [] [y p1y] /(has_nthP x)[j].',\n", "   'rewrite size_take leq_min => /andP[ltji _]; rewrite nth_take // => yFq1j.',\n", "   'by have /hasP[] := before_find x ltji; exists y; last by rewrite cfaceC.',\n", "   'have p1Fq1: has (mem (fband p1)) q1.',\n", "   'apply/hasP; exists (last enx q1).',\n", "   \"by case: (q1) ntq1 => //= nx' q11 _; apply: mem_last.\",\n", "   'by apply/hasP; exists enx; first by rewrite mem_rcons mem_head.',\n", "   'have lt_i_q1: i < size q1 by rewrite -has_find.',\n", "   'have /hasP[y p1y y1Fy] := nth_find x p1Fq1.',\n", "   'move: (simple_uniq Up1) Upq2 (last_rcons y p enx) {Up}; rewrite -/p1.',\n", "   'case/splitPr Dp: p1 / p1y => [p2 p3] Up.',\n", "   'rewrite last_cat /= has_cat => /norP[Up2q Up3q] Lp3.',\n", "   'pose q3 := q2 ++ belast y p3; pose q := enx :: q3.',\n", "   'have{y1Fy} URq: scycle rlink q.',\n", "   'apply/andP; split.',\n", "   'rewrite /= -{2}Lp3 rcons_cat -lastI cat_path.',\n", "   'rewrite -(cat_take_drop i q1) -/q2 (drop_nth x) // cat_path in enxRq1.',\n", "   'case/and3P: enxRq1 => ->; rewrite (rlinkFr y1Fy) /= => -> _.',\n", "   'by have:= xRp1; rewrite Dp cat_path /= => /and3P[].',\n", "   'rewrite -(simple_rot 1) rot1_cons rcons_cat -Lp3 -lastI simple_cat Up3q /=.',\n", "   'have:= Uq1; rewrite -(cat_take_drop i q1) simple_cat => /andP[-> _].',\n", "   'by have:= Up1; rewrite Dp simple_cat => /and3P[].',\n", "   'have p3_p: {subset (belast y p3) <= p}.',\n", "   'have ->: p = behead (belast x p1) by rewrite belast_rcons.',\n", "   'rewrite Dp belast_cat /= -cat_rcons -lastI /=.',\n", "   'by move=> z p3z; rewrite mem_cat p3z orbT.',\n", "   'have q3_hc: {subset q3 <= hc}.',\n", "   'move=> z; rewrite mem_cat => /orP[/mem_take q1z | /p3_p/p_hc//].',\n", "   'by have /andP[] := allP q1_ac z q1z.',\n", "   \"have dNq'x: (x \\\\in diskN q) = false.\",\n", "   'rewrite (fclosed1 (diskN_node q)) -[node x]e2c -/enx.',\n", "   'apply: diskN_edge_ring (mem_head _ _) => //.',\n", "   'case/andP: URq (q3_hc (node x)); rewrite /q hc_nx.',\n", "   \"case: (q3) => [|y2 [_ _|]] //=; first by rewrite /rlink e2c bridge'Gc.\",\n", "   'by move/implyP; rewrite inE implybF e2c.',\n", "   'suffices{IHn} dNq_dN: {subset diskN q <= diskN xp}.',\n", "   \"have{hc_nx} hc'enx: enx \\\\notin hc by rewrite hcE hc_nx.\",\n", "   \"case: {IHn}(IHn _ hc'enx q3) => // [|_ /dNq_dN/dN_ac//].\",\n", "   'apply: leq_trans le_dN_n; rewrite -/q [rhs in _ < rhs](cardD1 x) dNx ltnS.',\n", "   'apply/subset_leq_card/subsetP=> z dNq_z; rewrite !inE dNq_dN // andbT.',\n", "   \"by apply: contraFN dNq'x => /eqP <-.\",\n", "   'move=> z0 /(@hasP _ _ q)[z2 qz2 /connectP[r z1Dr ->]]; move: z1Dr.',\n", "   'set z1 := finv node z2.',\n", "   'have{qz2}: z1 \\\\in diskN xp /\\\\ z1 \\\\in diskN q.',\n", "   'rewrite !(fclosed1 (diskN_node _) z1) (f_finv nodeI).',\n", "   'split; last by rewrite diskN_E qz2.',\n", "   'move: qz2; rewrite -[q]cat_cons mem_cat => /orP[q2z2 | p3z2]; last first.',\n", "   'by rewrite diskN_E !inE p3_p ?orbT.',\n", "   'have Uq2p: ~~ has (mem (fband xp)) q2.',\n", "   'have: ~~ has (mem (fband q2)) p1 by rewrite Dp has_cat negb_or Up2q.',\n", "   'apply: contra => /hasP[t q2t /(@hasP _ _ xp)[u pu uFt]].',\n", "   'move: pu; rewrite inE => /predU1P[Du | pu].',\n", "   'apply/hasP; exists enx; first by rewrite mem_rcons mem_head.',\n", "   'by apply/hasP; exists t; rewrite // cface1 nodeK -Du cfaceC.',\n", "   'apply/hasP; exists u; first by rewrite mem_rcons mem_behead.',\n", "   'by apply/hasP; exists t; last by rewrite cfaceC.',\n", "   'case/andP: URq Uq2p; rewrite /q /= rcons_cat /q3.',\n", "   'case/splitPl: q2z2 => q4 q5 <-; rewrite -catA cat_path has_cat.',\n", "   'case/andP=> enxRq4 _ _ /norP[Uq4p _] {q5}.',\n", "   'elim: q4 (enx) enxRq4 dNenx Uq4p => //= v q4 IHq u.',\n", "   \"rewrite {1}/rlink cface1 => /andP[fuRv vRq4] dNu /norP[xpF'x xpF'q4].\",\n", "   'suffices: v \\\\in diskF xp by case/andP=> _ /IHq->.',\n", "   'rewrite -(closed_connect (diskF_face xp) fuRv) inE /=.',\n", "   \"rewrite (closed_connect (fbandF xp) fuRv) xpF'x.\",\n", "   'by rewrite (fclosed1 (diskN_node xp)) edgeK dNu.',\n", "   \"elim: r {z2}z1 => [|z2 r IHr] z1 [dNz1 dNq_z1] //= /andP[] /andP[q'z1 z1Cz2].\",\n", "   'apply: {r}IHr; rewrite !(fclosed1 (diskN_node _) z2).',\n", "   'case/clinkP: z1Cz2 => <- //; rewrite -(canRL edgeK (e2c _)).',\n", "   'rewrite !diskN_E -!(fclosed1 (diskE_edge _ _)) // ![z1 \\\\in diskE _]inE /=.',\n", "   \"rewrite {}dNz1 dNq_z1 q'z1 orbT andbT orbC; split=> //.\",\n", "   'have {1}->: xp = belast x p1 by rewrite belast_rcons.',\n", "   \"rewrite Dp belast_cat /= -cat_rcons -lastI {q'z1}(contra _ q'z1) //.\",\n", "   'rewrite -[q]cat_cons !mem_cat => /orP[xp2z1 | ->]; last exact: orbT.',\n", "   'have Uqp2: ~~ has (mem (fband q)) p2.',\n", "   'apply/hasP=> [] [t p2t /(@hasP _ _ q)[u qu tFu]].',\n", "   'have:= qu; rewrite -(mem_rot 1) rot1_cons rcons_cat -Lp3 -lastI mem_cat.',\n", "   'case/orP=> [q2u | p3u].',\n", "   'by case/hasP: Up2q; exists t; last by apply/hasP; exists u.',\n", "   'have:= Up1; rewrite Dp simple_cat => /and3P[_ /hasP[]].',\n", "   'by exists u; last by apply/hasP; exists t; rewrite 1?cfaceC.',\n", "   \"case/negP: dNq'x; have:= xRp1; rewrite Dp cat_path => /andP[xRp2 _].\",\n", "   'case/splitPl: xp2z1 xRp2 Uqp2 dNq_z1 => p4 p5 <- {z1}.',\n", "   \"rewrite cat_path has_cat => /andP[xRp4 _] /norP[qF'p4 _] {p5}.\",\n", "   \"elim/last_ind: p4 xRp4 qF'p4 => // r z IHr.\",\n", "   'rewrite last_rcons rcons_path has_rcons {2}/rlink /= cface1.',\n", "   \"case/andP=> xRp ferFz /norP[qF'z qF'r] dNq_z.\",\n", "   'have: z \\\\in diskF q by apply/andP.',\n", "   'rewrite -(closed_connect (diskF_face q) ferFz) => /andP[_] /=.',\n", "   'by rewrite (fclosed1 (diskN_node q)) edgeK; apply: IHr.',\n", "   'Qed.']},\n", " {'Let': 'Let cface_ac_h x y : x \\\\in ac -> cface x y -> cface (h x) (h y).'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let cface_h_ac x u : x \\\\in ac -> cface (h x) u -> exists2 y, cface x y & h y = u.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let hFn := preembedding_arity embed_h.'},\n", " {'Lemma': ['Lemma cface_inj_embed x y : x \\\\in ac -> cface x y -> h x = h y -> x = y.',\n", "   'Proof.',\n", "   'move=> ac_x xFy eq_hxy.',\n", "   'have iter_hF n: h (iter n face x) = iter n face (h x).',\n", "   'by elim: n (x) ac_x => // n IHn z ac_z; rewrite !iterSr -hF ?IHn -?fclosed1.',\n", "   'have eq_iFxy: findex face (h x) (h y) = findex face x y.',\n", "   'by rewrite -{1}(iter_findex xFy) iter_hF findex_iter // hFn // findex_max.',\n", "   'by rewrite -(iter_findex xFy) -eq_iFxy eq_hxy findex0.',\n", "   'Qed.']},\n", " {'Definition': 'Definition pre_hom_ring x p := [/\\\\ path rlink x p, {subset x :: p <= ac} & scycle rlink (map h (x :: p))].'},\n", " {'Lemma': ['Lemma intro_pre_hom_ring x p : path rlink x p -> rlink (h (last x p)) (h x) -> {subset x :: p <= ac} -> simple (map h (x :: p)) -> pre_hom_ring x p.',\n", "   'Proof.',\n", "   'move=> xRp Lhp p_ac Uhp; split=> //; rewrite /scycle {}Uhp andbT.',\n", "   'rewrite /= rcons_path last_map {}Lhp andbT.',\n", "   'move/allP: p_ac; elim: p x xRp => //= y p IHp x /andP[xRy yRp] /andP[ac_x p_ac].',\n", "   'have ac_ex: edge x \\\\in ac by rewrite (closed_connect acF xRy); case/andP: p_ac.',\n", "   'by rewrite IHp // /rlink -embed_functor ?cface_ac_h.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma trivial_hom_ring x p : pre_hom_ring x p -> fcard face (diskF (map h (x :: p))) <= 0 -> rlink (last x p) x.',\n", "   'Proof.',\n", "   'rewrite leqNgt => xHRp /(fcard0P (diskF_face _))/(introN existsP)/existsPn-pF_0.',\n", "   'elim: {p}_.+1 x {-2}p (ltnSn (size p)) pF_0 xHRp => // n IHn x p.',\n", "   'rewrite ltnS; set xp := x :: p => le_p_n pF_0 [xRp p_ac URhp].',\n", "   'set y := last x p; have xp_y: y \\\\in xp := mem_last x p.',\n", "   'have xp_hy: h y \\\\in map h xp := map_f h xp_y.',\n", "   'have ac_y: ac y := p_ac y xp_y.',\n", "   'have xpFnhy: node (h y) \\\\in fband (map h xp).',\n", "   'apply: contraR (pF_0 (node (h y))); rewrite !inE => ->.',\n", "   'by rewrite -(fclosed1 (diskN_node _)) diskN_E xp_hy.',\n", "   'have:= xpFnhy; rewrite unfold_in has_map => /hasP[z xpz] /=.',\n", "   'rewrite cfaceC => zFnhy.',\n", "   'have p_hz: h z \\\\in map h xp by apply: map_f.',\n", "   'case/splitPl Dp: p / (xpz) => [p1 p2 Lp1].',\n", "   'case/lastP: p2 Dp => [|p2 y1] Dp.',\n", "   \"by rewrite /y Dp cats0 Lp1 -{1}[h z]nodeK -cface1 cfaceC bridge'Gm in zFnhy.\",\n", "   'have Dy1: y1 = y by rewrite /y Dp last_cat last_rcons.',\n", "   'case/lastP: p1 => [|p1 z1] /= in Lp1 Dp.',\n", "   'case/andP: URhp; rewrite /= Dp map_rcons !rcons_path last_rcons => /andP[_].',\n", "   'rewrite Lp1 (rlinkFr zFnhy) Dy1 -[node (h y)]nodeK.',\n", "   \"by rewrite /rlink -cface1r cface1 (canRL nodeK (n3m _)) bridge'Gm.\",\n", "   'rewrite last_rcons in Lp1; rewrite {z1}Lp1 {y1}Dy1 in Dp.',\n", "   'pose eny := edge (node y).',\n", "   'have ac_eny: eny \\\\in ac by rewrite (fclosed1 acF) nodeK.',\n", "   'have h_eny: h eny = edge (node (h y)) by rewrite -[y]nodeK hF ?faceK.',\n", "   'have enyRz: rlink eny z.',\n", "   'case p_hny: (node (h y) \\\\in map h xp).',\n", "   'case/mapP: p_hny => [t xpt Dht].',\n", "   'case/splitPl Dp3: p / (xpt) => [p3 p4 Lp3].',\n", "   'have Dp4: p4 = [:: y].',\n", "   'case: p4 => [|t1 [|t2 p4]] in Dp3 *.',\n", "   'rewrite cats0 in Dp3; rewrite -Dp3 -/y in Lp3.',\n", "   \"by have /idP[] := bridge'Gm (h t); rewrite -{2}Dht cface1r nodeK Lp3.\",\n", "   'by rewrite /y Dp3 last_cat.',\n", "   'case/andP: URhp; rewrite /= Dp3 map_cat rcons_cat -cat_cons cat_path.',\n", "   'rewrite last_map Lp3 simple_cat /= => /and3P[_ htRt1 _] /and3P[_ _].',\n", "   'rewrite simple_cons -map_cons unfold_in => /andP[/hasP[]].',\n", "   'exists (h y); first by rewrite map_f // /y Dp3 last_cat /= mem_last.',\n", "   'by rewrite -(same_cface htRt1) -Dht cface1 nodeK.',\n", "   'have Dt: t = node y.',\n", "   'move: xRp; rewrite Dp3 cat_path Lp3 Dp4 => /and3P[_ tRy _].',\n", "   'have ac_et: edge t \\\\in ac by rewrite (closed_connect acF tRy).',\n", "   'apply/edgeI/cface_inj_embed=> //; first by rewrite cface1r nodeK.',\n", "   'by rewrite embed_functor -?Dht // p_ac.',\n", "   'case: p2 Dp (congr1 (last x \\\\o belast x) Dp) => [|z1 p2] Dp.',\n", "   'rewrite Dp3 Dp4 /= !belast_cat !last_cat /= last_rcons Lp3 => <-.',\n", "   'by rewrite /rlink e2c Dt.',\n", "   'rewrite Dp3 Dp4 /= !belast_cat /= Lp3 !last_cat /= belast_rcons /= => Lp2.',\n", "   'case/andP: URhp => _; rewrite Dp (lastI z1) -Lp2 cat_rcons -cat_cons.',\n", "   'rewrite map_cat !map_cons simple_cat !simple_cons => /and4P[_ _].',\n", "   'by rewrite unfold_in !map_rcons !has_rcons orbCA -Dht zFnhy.',\n", "   'pose q := rcons p2 eny.',\n", "   'have henyFy: cface (h eny) (h y) by rewrite cface1 h_eny nodeK.',\n", "   'have zHRq: pre_hom_ring z q.',\n", "   'apply: intro_pre_hom_ring => [||t|].',\n", "   'move: xRp; rewrite Dp cat_path last_rcons => /andP[_].',\n", "   'by rewrite !rcons_path {4}/rlink cface1r nodeK.',\n", "   'by rewrite /rlink last_rcons cfaceC (same_cface zFnhy) h_eny e2m.',\n", "   'rewrite -rcons_cons mem_rcons inE => /predU1P[-> // | p2t].',\n", "   'by rewrite p_ac // Dp cat_rcons -rcons_cons -cats1 inE !mem_cat p2t !orbT.',\n", "   'rewrite -{q}rcons_cons map_rcons simple_rcons.',\n", "   'rewrite (closed_connect (fbandF _) henyFy) -simple_rcons.',\n", "   'case/andP: URhp => _; rewrite Dp cat_rcons -cat_cons -rcons_cons.',\n", "   'by rewrite map_cat -map_rcons simple_cat => /and3P[].',\n", "   'rewrite -(last_rcons z p2 eny) -/q IHn // => [|u].',\n", "   'apply: leq_trans le_p_n; rewrite Dp size_cat !size_rcons.',\n", "   'by rewrite addSnnS leq_addl.',\n", "   'have hxpFeny: h eny \\\\in fband (map h xp) by apply/hasP; exists (h y).',\n", "   'have Ehzq: chord_ring (map h xp) (h eny) = map h (rotr 1 (z :: q)).',\n", "   'rewrite -rcons_cons rotr1_rcons !map_cons; congr (_ :: _).',\n", "   'have <-: h y = fproj (map h xp) (h eny).',\n", "   'have [xpj] := fprojP hxpFeny.',\n", "   'by rewrite (same_cface henyFy); apply: (scycle_cface URhp).',\n", "   'rewrite h_eny e2m.',\n", "   'have <-: h z = fproj (map h xp) (node (h y)).',\n", "   'have [xpj] := fprojP xpFnhy.',\n", "   'by rewrite -(same_cface zFnhy); apply: (scycle_cface URhp).',\n", "   'case/andP: URhp => _ /simple_uniq.',\n", "   'rewrite -map_cons -/xp -(rotrK 1 xp) map_rot rot_uniq => Up.',\n", "   'rewrite arc_rot //; last by apply map_f; rewrite mem_rotr.',\n", "   'move: Up; rewrite /xp Dp -cat_cons -rcons_cat rotr1_rcons -rcons_cons.',\n", "   'by rewrite cat_rcons map_cons map_cat !map_cons; apply: right_arc.',\n", "   'rewrite -[z :: q](rotrK 1) map_rot /= diskF_rot -Ehzq.',\n", "   'rewrite diskF_chord_ring // ?h_eny ?e2m //.',\n", "   'by rewrite negb_and pF_0 orbT.',\n", "   'by rewrite /xp Dp (headI p2); case: (p1) => [|? []].',\n", "   'rewrite -(fclosed1 (diskE_edge _ _)) // inE /= p_hny -map_cons -/xp.',\n", "   'by rewrite -(fclosed1 (diskN_node _)) diskN_E xp_hy.',\n", "   'have ac_ny: node y \\\\in ac.',\n", "   'by rewrite -[node y]e2c (closed_connect acF enyRz) p_ac.',\n", "   'have h_ny: h (node y) = node (h y).',\n", "   'by apply edgeI; rewrite -h_eny -embed_functor.',\n", "   'pose enny := edge (node (node y)).',\n", "   'have ac_enny: enny \\\\in ac by rewrite (fclosed1 acF) nodeK.',\n", "   'have h_enny: h enny = edge (node (node (h y))).',\n", "   'by rewrite -h_ny -[node y]nodeK (hF ac_enny) faceK.',\n", "   'have hyRx: rlink (h y) (h x).',\n", "   'by case/andP: URhp; rewrite /= rcons_path last_map => /andP[_].',\n", "   'have ennyRx: rlink enny x.',\n", "   \"have [|p'nnhy] := boolP (node (node (h y)) \\\\in map h xp).\",\n", "   'case/mapP=> t xpt Dht; case/splitPl Dp4: p / xpt => [p3 p4 Lp3].',\n", "   'have Dp3: p3 = [::].',\n", "   'case: p3 => //= t1 p3 in Dp4 Lp3 *; case/andP: URhp => _.',\n", "   'rewrite /rlink -[h y]n3m cface1 nodeK Dht in hyRx.',\n", "   'rewrite map_cons simple_cons {1}Dp4 -cat_cons lastI Lp3 cat_rcons.',\n", "   'by rewrite map_cat fband_cat /= fband_cons cfaceC hyRx !orbT.',\n", "   'rewrite -{}Lp3 {p3 p4 Dp4}Dp3 /= in Dht.',\n", "   'have Dp1: p1 = [::]; last rewrite {p1}Dp1 /= in Dp.',\n", "   'case: p1 Dp => // x1 p1 Dp.',\n", "   'case/andP: URhp; rewrite Dp /= => /andP[hxRx1 _].',\n", "   'rewrite !simple_cons cat_rcons map_cat fband_cat /= => /and3P[_ /negP[]].',\n", "   'rewrite orbC fband_cons -(same_cface hxRx1) cfaceC -Dht cface1r nodeK.',\n", "   'by rewrite zFnhy.',\n", "   'suffices Dx: x = node (node y) by rewrite /rlink e2c -Dx.',\n", "   'move: xRp; rewrite Dp /= => /andP[xRz _].',\n", "   'have ac_ex: edge x \\\\in ac by rewrite (closed_connect acF xRz) p_ac.',\n", "   'rewrite /rlink e2c -[node y]nodeK -/enny -cface1 in enyRz.',\n", "   'rewrite cfaceC -(same_cface xRz) in enyRz; apply/edgeI/cface_inj_embed=> //.',\n", "   'by rewrite (embed_functor (p_ac _ (mem_head _ _)) ac_ex) -Dht.',\n", "   'pose q := rcons p1 enny.',\n", "   'have hennyFz: cface (h enny) (h z) by rewrite cface1 h_enny nodeK cfaceC.',\n", "   'have xHRq: pre_hom_ring x q.',\n", "   'apply: intro_pre_hom_ring => [||t|].',\n", "   'move: xRp; rewrite Dp cat_path andbC => /andP[_].',\n", "   'by rewrite !rcons_path -(rlinkFr enyRz) e2c {4}/rlink cface1r nodeK.',\n", "   'by rewrite last_rcons h_enny /rlink e2m (canRL nodeK (n3m _)) -cface1.',\n", "   'rewrite -rcons_cons mem_rcons inE => /predU1P[-> //|p1t].',\n", "   'by rewrite p_ac // Dp cat_rcons -cat_cons mem_cat p1t.',\n", "   'rewrite {}/q -rcons_cons map_rcons simple_rcons.',\n", "   'rewrite (closed_connect (fbandF _) hennyFz) -simple_rcons.',\n", "   'case/andP: URhp => _; rewrite -map_rcons Dp -cat_cons map_cat.',\n", "   'by rewrite simple_cat => /and3P[].',\n", "   'rewrite -(last_rcons x p1 enny) -/q IHn // => [|u].',\n", "   'apply: leq_trans le_p_n; rewrite Dp size_cat !size_rcons.',\n", "   'by rewrite -addSnnS leq_addr.',\n", "   'have xpFhenny: h enny \\\\in fband (map h xp) by apply/hasP; exists (h z).',\n", "   'rewrite /rlink -[h y]n3m cface1 nodeK in hyRx.',\n", "   'have xpFnnhy: node (node (h y)) \\\\in fband (map h xp).',\n", "   'by apply/hasP; exists (h x); first apply: mem_head.',\n", "   'have Ehzq: chord_ring (map h xp) (h enny) = map h (rotr 1 (x :: q)).',\n", "   'rewrite /q -rcons_cons rotr1_rcons map_cons; congr (_ :: _).',\n", "   'have <-: h z = fproj (map h xp) (h enny).',\n", "   'have [xpj] := fprojP xpFhenny.',\n", "   'by rewrite (same_cface hennyFz); apply: (scycle_cface URhp).',\n", "   'have <-: h x = fproj (map h xp) (edge (h enny)).',\n", "   'have [xpj] := fprojP xpFnnhy.',\n", "   'rewrite (same_cface hyRx) h_enny e2m; apply: (scycle_cface URhp) => //.',\n", "   'exact: mem_head.',\n", "   'case/andP: URhp => _ /simple_uniq.',\n", "   'by rewrite /xp Dp cat_rcons map_cons map_cat !map_cons; apply: left_arc.',\n", "   'rewrite -[x :: q](rotrK 1) map_rot /= diskF_rot -Ehzq.',\n", "   'rewrite diskF_chord_ring // ?h_enny ?e2m //.',\n", "   'by rewrite negb_and pF_0 orbT.',\n", "   'by rewrite /xp Dp (headI p2); case: (p1) => [|x1 []].',\n", "   \"rewrite -(fclosed1 (diskE_edge _ _)) // inE /= p'nnhy.\",\n", "   'by rewrite -!(fclosed1 (diskN_node _)) diskN_E xp_hy.',\n", "   'by rewrite /rlink e2c (canRL nodeK (n3c _)) -?cface1 // [~~ _]ac_bc in ennyRx.',\n", "   'Qed.']},\n", " {'Let': 'Let rcN : fclosed node rc.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Lemma': ['Lemma edge_perimeter x : (x \\\\in bc) || (edge x \\\\in bc).',\n", "   'Proof.',\n", "   'rewrite -negb_and; apply/andP => [] [/= rc_x rc_ex].',\n", "   'have fxx: face x = x.',\n", "   'apply: (scycle_cface UNrc) => //; last by rewrite cfaceC fconnect1.',\n", "   'by rewrite (fclosed1 rcN) -(canRL edgeK (e2c x)).',\n", "   'have cycFx: fcycle face [:: x] by rewrite /= fxx eqxx.',\n", "   'have /idPn[] := allP (embeddable_ring embedGc) _ rc_x.',\n", "   'by rewrite /good_ring_arity (order_cycle cycFx) // mem_head.',\n", "   'Qed.']},\n", " {'Notation': 'Notation erc := (map edge rc).'},\n", " {'Let': 'Let Drrrc : rev_ring rrc = erc.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let URrrc : scycle rlink rrc.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let URerc : scycle rlink erc.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Lemma': ['Lemma chordless_perimeter x : x \\\\in bc -> edge x \\\\in bc -> (x \\\\in ac) || (edge x \\\\in ac).',\n", "   'Proof.',\n", "   'have EercF: fband erc =i fband rc.',\n", "   'move=> y; rewrite -Drrrc fband_rev_ring //.',\n", "   'by apply: eq_has_r => z; rewrite mem_rev.',\n", "   'move=> bc_x bc_ex; have [cycRerc /simple_uniq Uerc] := andP URerc.',\n", "   'rewrite -negb_and; apply/andP=> [] [rcFx rcFex].',\n", "   'have erc_ok: proper_ring erc.',\n", "   'move: rcFx cycRerc (edge_perimeter (head x rc)).',\n", "   \"case: (rc) => [|x1 [|x2 []]] //= _; first by rewrite /rlink cfaceC bridge'Gc.\",\n", "   'by rewrite !inE eqxx (inj_eq edgeI) => _ /norP[_].',\n", "   'move: x bc_x bc_ex rcFx rcFex.',\n", "   'have rrc_ok: proper_ring rrc by rewrite -(proper_rev_ring geoGc) Drrrc.',\n", "   'have nt_dFerc x: x \\\\in fband erc -> edge x \\\\in fband erc -> x \\\\in diskE erc -> exists y, y \\\\in diskF (chord_ring erc x).',\n", "   'move=> ercFx ercFex dEx; set rx := chord_ring erc x.',\n", "   'have URrx: scycle rlink rx by apply: scycle_chord_ring.',\n", "   'have rx_ok: proper_ring rx by apply: proper_chord_ring.',\n", "   'have rx_erc: {subset behead rx <= erc}.',\n", "   'have [[erc_jx xFj] [erc_jex exFj]] := (fprojP ercFx, fprojP ercFex).',\n", "   'have [|i p1 _ Dp1 ->] := rot_to_arc Uerc erc_jex erc_jx.',\n", "   'apply: contraTneq exFj => ->; rewrite cfaceC -(same_cface xFj).',\n", "   \"by rewrite bridge'Gc.\",\n", "   'rewrite -cat_cons {p1}Dp1 => Derc y rx_y.',\n", "   'by rewrite -(mem_rot i) Derc mem_cat rx_y.',\n", "   'have{ercFx ercFex dEx} dN_bc: {subset diskN rx <= bc}.',\n", "   'move=> y; rewrite diskN_chord_ring // => /andP[_ /=].',\n", "   'by rewrite -Drrrc diskN_rev_ring ?inE //= diskN_E inE /= mem_rev => /norP[].',\n", "   'move: URrx rx_ok rx_erc dN_bc.',\n", "   'elim: {x rx}_.+1 {-2}rx (ltnSn (size rx)) => // n IHn p; rewrite ltnS => lepn.',\n", "   'move=> URp p_ok p_erc dN_bc; apply/existsP/pred0P => dF_0.',\n", "   'have [cycRp /simple_uniq Up] := andP URp.',\n", "   'case Dp: p (p_ok) => [|x p1] // _.',\n", "   'have px: x \\\\in p by rewrite Dp mem_head.',\n", "   'have dNx: x \\\\in diskN p by rewrite diskN_E !inE px.',\n", "   'have bcx: x \\\\in bc := dN_bc x dNx.',\n", "   'have [[pF_F dN_N] dE_E] := (fbandF p, diskN_node p, diskE_edge planarGc URp).',\n", "   'pose efex := edge (face (edge x)); pose enx := edge (node x).',\n", "   'have f_efex: face efex = node x by rewrite /efex -{1}(n3c bcx) !nodeK.',\n", "   'have [dEefex|] := boolP (efex \\\\in diskE p).',\n", "   'have pFefex: efex \\\\in fband p.',\n", "   'apply: contraFT (dF_0 (node x)); rewrite (fclosed1 pF_F) f_efex /=.',\n", "   'by rewrite inE /= -(fclosed1 dN_N) => ->.',\n", "   'have pFeefex: edge efex \\\\in fband p.',\n", "   'apply/hasP; exists (next p x); first by rewrite mem_next.',\n", "   'by rewrite e2c -cface1; apply: next_cycle cycRp px.',\n", "   'pose y := fproj p efex.',\n", "   'have p1y: y \\\\in p1.',\n", "   'have [] := fprojP pFefex; rewrite -/y Dp inE => /predU1P[] // ->.',\n", "   \"by rewrite -{1}[x]nodeK -cface1r cface1 f_efex bridge'Gc.\",\n", "   'case/splitPr Dp1: p1 / p1y => [p2 p3].',\n", "   'have Dp2: efex :: p2 = chord_ring p efex.',\n", "   'congr (_ :: _); rewrite -/y e2c /arc; set z := fproj p (face (edge x)).',\n", "   'have ->: index z p = 1.',\n", "   'have[]:= fprojP pFeefex; rewrite e2c -/z -cface1 cfaceC {2}Dp /= => pz.',\n", "   \"case: eqP => [<- | _ zFex]; first by rewrite bridge'Gc.\",\n", "   'move: cycRp; rewrite Dp; case: (p1) Dp => [|z1 p5] //= Dp /andP[xRz1 _].',\n", "   'rewrite -[z1](scycle_cface URp pz) ?eqxx ?(same_cface zFex) //.',\n", "   'by rewrite Dp !inE eqxx orbT.',\n", "   'rewrite Dp rot1_cons Dp1 rcons_cat index_cat /= eqxx addn0.',\n", "   'rewrite [y \\\\in p2](contraTF _ Up) ?take_size_cat //.',\n", "   'by rewrite Dp /= Dp1 cat_uniq /= => ->; rewrite !andbF.',\n", "   'have{IHn} [|||z p2z|z|z] := IHn (efex :: p2).',\n", "   'by apply: leq_trans lepn; rewrite Dp Dp1 /= size_cat /= addnS leq_addr.',\n", "   'by rewrite Dp2; apply scycle_chord_ring.',\n", "   'by rewrite Dp2; apply proper_chord_ring.',\n", "   'by rewrite p_erc // Dp Dp1 /= mem_cat p2z.',\n", "   'by rewrite Dp2 diskN_chord_ring // => /andP[_]; apply: dN_bc.',\n", "   'by rewrite Dp2 diskF_chord_ring // inE /= [X in _ && X]dF_0 andbF.',\n", "   'rewrite -(fclosed1 dE_E) inE /= (fclosed1 dN_N) edgeK dNx andbT negbK => pfex.',\n", "   'case: p1 => [|fex p2] in Dp; first by rewrite Dp in p_ok.',\n", "   'have Dfex: fex = face (edge x).',\n", "   'apply: (scycle_cface URp) => //; first by rewrite Dp !inE eqxx orbT.',\n", "   'by have:= cycRp; rewrite Dp /= /rlink cface1 cfaceC => /andP[].',\n", "   'have pFenx: enx \\\\in fband p.',\n", "   'by rewrite (fclosed1 pF_F) nodeK; apply: subsetP (ring_fband _) _ px.',\n", "   'have pFeenx: edge enx \\\\in fband p.',\n", "   'apply/hasP; exists (next p fex); first by rewrite mem_next Dfex.',\n", "   'by rewrite e2c -f_efex -cface1 Dfex; apply: next_cycle cycRp pfex.',\n", "   'have dEenx: enx \\\\in diskE p.',\n", "   'rewrite -(fclosed1 dE_E) inE /= -(fclosed1 dN_N) dNx andbT Dp /= inE.',\n", "   'have [_ /negPf->/=] := cubicP cubicGc x bcx.',\n", "   'have rc_efex: efex \\\\in rc.',\n", "   'by rewrite -(mem_map edgeI) e2c -Dfex p_erc // Dp mem_head.',\n", "   'have: 2 < arity efex < 7.',\n", "   'by rewrite -mem_iota !inE [_ || _](allP (embeddable_ring embedGc)).',\n", "   'rewrite ltnNge andbC /arity => /andP[_]; apply: contra => pnx.',\n", "   'rewrite (eq_card (fconnect_cycle _ (mem_head _ [:: node x]))) ?card_size //.',\n", "   'have fnxFefex: cface (face (node x)) efex by rewrite -f_efex -!cface1.',\n", "   'rewrite /= f_efex eqxx andbT; apply/eqP/(scycle_cface UNrc)=> //.',\n", "   'by rewrite (fclosed1 rcN) -(mem_map edgeI) faceK p_erc ?Dp.',\n", "   'have Dp2: enx :: p2 = chord_ring p enx.',\n", "   'congr (_ :: _); apply/esym.',\n", "   'have <-: x = fproj p enx.',\n", "   'have [pj enxFj] := fprojP pFenx; apply: (scycle_cface URp) => //.',\n", "   'by rewrite -{1}[x]nodeK -cface1.',\n", "   'have: cface (node x) (head x p2).',\n", "   'by move: cycRp; rewrite -f_efex -cface1 Dp /= headI Dfex => /and3P[].',\n", "   'case: p2 Dp => [|z p3] Dp /= => [|nxFz].',\n", "   \"by rewrite -{2}[x]nodeK -cface1r bridge'Gc.\",\n", "   'have <-: z = fproj p (edge enx).',\n", "   'have [pj] := fprojP pFeenx; rewrite {1}e2c (same_cface nxFz).',\n", "   'by apply: (scycle_cface URp); rewrite // Dp !inE eqxx !orbT.',\n", "   'by move: Up; rewrite Dp -(cat1s fex); apply: right_arc.',\n", "   'have{IHn} [|||z /= p2z|z|z] := IHn (enx :: p2).',\n", "   'by apply: leq_trans lepn; rewrite Dp.',\n", "   'by rewrite Dp2; apply: scycle_chord_ring.',\n", "   'by rewrite Dp2; apply: proper_chord_ring.',\n", "   'by rewrite p_erc // Dp mem_behead.',\n", "   'by rewrite Dp2 diskN_chord_ring // => /andP[_ /dN_bc].',\n", "   'by rewrite Dp2 diskF_chord_ring // inE /= [X in _ && X]dF_0 andbF.',\n", "   'have dFerc_ac x: x \\\\in fband erc -> edge x \\\\in fband erc -> x \\\\in diskE erc -> diskF (chord_ring erc x) =i ac.',\n", "   'move=> ercFx ercFex dEx; set rx := chord_ring erc x.',\n", "   'have dF_ac: {subset diskF rx <= ac}.',\n", "   'by move=> y; rewrite diskF_chord_ring // !inE EercF => /and3P[].',\n", "   'have URrx: scycle rlink rx by apply: scycle_chord_ring.',\n", "   'have dN_dF: {in ac, {subset diskN rx <= diskF rx}}.',\n", "   'move=> y; rewrite !inE /= -EercF.',\n", "   'by rewrite -(fband_chord_ring _ _ ercFx) // -/rx => /norP[-> _].',\n", "   'move=> z; apply/idP/idP=> [|ac_z]; first exact: dF_ac.',\n", "   'have [y dFy] := nt_dFerc x ercFx ercFex dEx; rewrite -/rx in dFy.',\n", "   'have ac_eey: edge (edge y) \\\\in ac by rewrite e2c dF_ac.',\n", "   'have [p [eyRp Lp nt_p] [_ p_hac]] := h_spoke ac_eey ac_z.',\n", "   'have{p_hac}: all (mem ac) p by move: p_hac; rewrite all_predI => /andP[].',\n", "   'case: p nt_p Lp eyRp => //= [y1 p] _ Lp /andP[eyRy1 y1Rp] /andP[_].',\n", "   'have{eyRy1} dFy1: y1 \\\\in diskF rx.',\n", "   'by rewrite -(closed_connect (diskF_face rx) eyRy1) e2c.',\n", "   'rewrite -{Lp}(closed_connect (diskF_face rx) Lp).',\n", "   'elim: p y1 dFy1 y1Rp => //= y2 p IHp y1 dFy1 /andP[y1Ry2 y2Rp] /andP[ac_y2].',\n", "   'apply: {p}IHp y2Rp; rewrite /rlink cface1 in y1Ry2.',\n", "   'rewrite -(closed_connect (diskF_face rx) y1Ry2) dN_dF //.',\n", "   'by rewrite inE /= (closed_connect (fbandF rc) y1Ry2).',\n", "   'by rewrite (fclosed1 (diskN_node rx)) edgeK; case/andP: dFy1.',\n", "   'move=> x bc_x bc_ex; rewrite /= -!EercF => ercFx ercFex.',\n", "   'have dEex: edge x \\\\in diskE erc.',\n", "   'rewrite inE /= (mem_map edgeI) [~~ _]bc_x -Drrrc diskN_rev_ring //= !inE.',\n", "   'apply: contra bc_ex => /hasP[y rrc_y /connectP[]].',\n", "   'rewrite mem_rev -{1}(f_finv nodeI y) -(fclosed1 rcN) in rrc_y.',\n", "   'by case=> [|z p] => [_ -> | ] //=; rewrite {1}/dlink /= mem_rev rrc_y.',\n", "   'have [||y dFy] := nt_dFerc _ ercFex; rewrite ?e2c //.',\n", "   'have ac_y: y \\\\in ac by rewrite dFerc_ac ?e2c in dFy.',\n", "   'have /idPn[]:= dFy; rewrite diskF_chord_ring ?e2c // inE /= dFerc_ac ?ac_y //.',\n", "   'by rewrite (fclosed1 (diskE_edge planarGc URerc)).',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma fcard_adj_perimeter x : x \\\\notin ac -> #|predI (cface x) [preim edge of fband rc]| = 2.',\n", "   'Proof.',\n", "   'case/negbNE/hasP=> y rc_y xFy.',\n", "   'rewrite (cardD1 y) (cardD1 (edge (node y))) !inE xFy cface1r nodeK xFy e2c.',\n", "   'have rc_ny: node y \\\\in rc by rewrite -(fclosed1 rcN).',\n", "   'case: eqP => [eny_y | _].',\n", "   'by have/norP[]:= edge_perimeter (node y); rewrite eny_y !negbK.',\n", "   'have rcFey: edge y \\\\in fband rc.',\n", "   'apply/hasP; exists (face (edge y)); last exact: fconnect1.',\n", "   'by rewrite (fclosed1 rcN) edgeK.',\n", "   'rewrite rcFey (subsetP (ring_fband _)) //=; apply/eqP/existsP=> [[z]].',\n", "   \"case/and4P=> eny'z y'z; rewrite {x xFy}(same_cface xFy) => yFz /= rcFez.\",\n", "   \"have bc_z: z \\\\in bc by apply: contra y'z => /(scycle_cface UNrc rc_y)->.\",\n", "   'have bc_ez: edge z \\\\in bc.',\n", "   \"apply: contra eny'z => /= rc_ez; apply/eqP/(scycle_cface URerc).\",\n", "   'by rewrite -[z]e2c map_f.',\n", "   'exact: map_f.',\n", "   'by rewrite cfaceC cface1 nodeK.',\n", "   'have/norP[]:= chordless_perimeter bc_z bc_ez.',\n", "   'by rewrite !negbK; split=> //; apply/hasP; exists y; rewrite // cfaceC.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma adj_kernel_min x : x \\\\notin ac -> exists2 y, y \\\\in ac & cface x (edge y).',\n", "   'Proof.',\n", "   'case/negbNE/hasP=> y rc_y xFy; have:= allP (embeddable_ring embedGc) _ rc_y.',\n", "   'rewrite -[_ y]mem_seq4 (mem_iota 3 4) andbC => /andP[_].',\n", "   'rewrite /order -(cardID [preim edge of fband rc]).',\n", "   'rewrite fcard_adj_perimeter ?(contraL (@ac_bc _)) // !ltnS lt0n.',\n", "   'by case/exists_inP=> z; exists (edge z); rewrite // e2c (same_cface xFy).',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma adj_kernel_max x : x \\\\notin ac -> #|predI (cface x) [preim edge of ac]| <= 4.',\n", "   'Proof.',\n", "   'case/negbNE/hasP=> y rc_y xFy; have:= allP (embeddable_ring embedGc) _ rc_y.',\n", "   'rewrite -[_ y]mem_seq4 (mem_iota 3 4) => /andP[_].',\n", "   'rewrite /order -(cardID [preim edge of fband rc]).',\n", "   'rewrite fcard_adj_perimeter ?(contraL (@ac_bc _)) //.',\n", "   'by congr (_ <= 4); apply: eq_card => z; rewrite !inE (same_cface xFy) andbC.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embed_full : {in ac &, forall x1 x2, edge (h x1) = h x2 -> edge x1 = x2}.',\n", "   'Proof.',\n", "   'suffices IHf5: ~ exists x1, exists2 p, [/\\\\ path rlink x1 p, last x1 p != x1 & {subset p <= ac}] & edge (h (last x1 p)) = h (edge x1) /\\\\ size p <= 5.',\n", "   \"move=> x1 x2 ac_x1 ac_x2 /= ehx1; apply/eqP/idPn=> x2'ex1; case: IHf5.\",\n", "   'have /radius2P[x0 ac_x0 ac_x0rad2] := embeddable_kernel embedGc.',\n", "   'have /(at_radius2P acF)[x01 [x10 [x0Fx01 x1Fx10]]] := ac_x0rad2 x1 ac_x1.',\n", "   'have /(at_radius2P acF)[x02 [x20 [x0Fx02 x2Fx20]]] := ac_x0rad2 x2 ac_x2.',\n", "   'move=> [ac_ex02 ex02Fex20] [ac_ex01 ex01Fex10].',\n", "   'exists (edge x1), [:: x10; edge x01; x02; edge x20; x2]; last first.',\n", "   'by split; rewrite //= e2c -ehx1 e2m.',\n", "   'rewrite eq_sym; split=> //=.',\n", "   'rewrite /rlink !e2c x1Fx10 cfaceC ex01Fex10 -(same_cface x0Fx01) x0Fx02.',\n", "   'by rewrite ex02Fex20 cfaceC x2Fx20.',\n", "   'apply/allP; rewrite /= ac_ex01 ac_x2 -(closed_connect acF x1Fx10) ac_x1.',\n", "   'rewrite -(closed_connect acF x0Fx02) ac_x0.',\n", "   'by rewrite -(closed_connect acF ex02Fex20) ac_ex02.',\n", "   'suffices IHf5: ~ exists x, exists2 p, pre_hom_ring x p /\\\\ ~~ rlink (last x p) x & size p <= 4.',\n", "   'move=> [x0 [p [x0Rp Lp p_ac] [Lhp ple5]]].',\n", "   'suffices{Lp Lhp}: exists2 q, [/\\\\ path rlink x0 q, last x0 q = last x0 p & {subset q <= ac}] & simple (map h q) /\\\\ size q <= size p.',\n", "   'case=> [[|x q] [x0Rq Lq q_ac] [Uq leqp]].',\n", "   'by rewrite -Lq /= eqxx in Lp.',\n", "   'case: IHf5; exists x, q; last exact: leq_trans leqp ple5.',\n", "   'have ac_x: x \\\\in ac by rewrite q_ac ?mem_head.',\n", "   'have{x0Rq Lq} /=[/andP[x0Rx xRq] Lq] := (x0Rq, Lq); split.',\n", "   'apply: intro_pre_hom_ring; rewrite // Lq /rlink Lhp cfaceC.',\n", "   'by rewrite cface_ac_h // cfaceC.',\n", "   'apply: contra Lp => qRx0; apply/eqP/edgeI.',\n", "   'have ac_eLp: edge (last x0 p) \\\\in ac.',\n", "   'by rewrite -Lq (closed_connect acF qRx0).',\n", "   'apply: cface_inj_embed => //.',\n", "   'by rewrite -Lq (same_cface qRx0) cfaceC.',\n", "   'by rewrite embed_functor // -Lq q_ac ?mem_last.',\n", "   'elim: p x0 x0Rp p_ac ple5 => [|x1 p IHp] x0 /=; first by exists [::].',\n", "   'case/andP=> [x0Rx1 x1Rp] /allP/=/andP[ac_x1 /allP p_ac] ple5.',\n", "   'have{IHp x1Rp} [q [x1Rq Lq q_ac] [Uq leqp]] := IHp _ x1Rp p_ac (ltnW ple5).',\n", "   \"have [| qF'x1] := boolP (h x1 \\\\in fband (map h q)); last first.\",\n", "   'exists (x1 :: q); split=> //; first by rewrite /= x0Rx1.',\n", "   'by apply/allP; rewrite /= ac_x1; apply/allP.',\n", "   \"by rewrite map_cons simple_cons qF'x1.\",\n", "   'rewrite unfold_in has_map => /hasP[x2 q_x2]; rewrite /= cfaceC => hx2Fx1.',\n", "   'have ac_x2: x2 \\\\in ac := q_ac _ q_x2.',\n", "   'have [x3 x2Fx3 eq_hx13] := cface_h_ac ac_x2 hx2Fx1.',\n", "   'have ac_x3: x3 \\\\in ac by rewrite -(closed_connect acF x2Fx3).',\n", "   'case/splitP: {q}q_x2 x1Rq Lq leqp Uq q_ac => [q1 q2].',\n", "   'rewrite cat_path last_cat last_rcons cat_rcons size_cat /=.',\n", "   'case/andP=> x1Rq1 x2Rq2 Lq2 leqp Uq q_ac.',\n", "   \"have [eq_x13 | x3'1] := eqVneq x1 x3.\",\n", "   'exists (x2 :: q2); split=> //.',\n", "   'by rewrite /= {1}/rlink (same_cface x0Rx1) eq_x13 cfaceC x2Fx3.',\n", "   'by move=> z q2z; rewrite q_ac // mem_cat q2z orbT.',\n", "   'by move: Uq; rewrite map_cat simple_cat => /and3P[_ _].',\n", "   'by rewrite (leqW (leq_trans _ leqp)) ?leq_addl.',\n", "   'case: q1 => [|x q] in leqp Uq q_ac x1Rq1.',\n", "   'have [x1Rx2 _] := andP x1Rq1.',\n", "   'have ac_ex1: edge x1 \\\\in ac by rewrite (closed_connect acF x1Rx2).',\n", "   'have:= cface_ac_h ac_ex1 x1Rx2.',\n", "   \"by rewrite cfaceC embed_functor // (same_cface hx2Fx1) bridge'Gm.\",\n", "   'case: IHf5; exists x, (rcons q x3); last first.',\n", "   'by rewrite size_rcons (leq_trans _ (leq_trans leqp ple5)) ?leq_addr.',\n", "   'have ac_x: x \\\\in ac := q_ac x (mem_head _ _).',\n", "   'move: x1Rq1; rewrite /= rcons_path => /andP[x1Rx xRq].',\n", "   'have ac_ex1: edge x1 \\\\in ac by rewrite (closed_connect acF x1Rx).',\n", "   'rewrite last_rcons; split; last first.',\n", "   \"apply: contra x3'1; rewrite -(rlinkFr x1Rx) /rlink cfaceC => ex3Rex1.\",\n", "   'apply/eqP/edgeI/(cface_inj_embed ac_ex1 ex3Rex1).',\n", "   'by rewrite !embed_functor ?eq_hx13 // -(closed_connect acF ex3Rex1).',\n", "   'apply: intro_pre_hom_ring => //.',\n", "   'by rewrite rcons_path -(rlinkFr x2Fx3).',\n", "   'by rewrite last_rcons eq_hx13 /rlink -embed_functor // cface_ac_h.',\n", "   'move=> y; rewrite -rcons_cons mem_rcons inE => /predU1P[-> // | qy].',\n", "   'by rewrite q_ac // mem_cat qy.',\n", "   'move: Uq; rewrite -rcons_cons -cat_rcons map_cat simple_cat !map_rcons.',\n", "   'rewrite -!rot1_cons !simple_rot 2!simple_cons andbC => /andP[_].',\n", "   'by congr (_ && _); rewrite (closed_connect (fbandF _) hx2Fx1) eq_hx13.',\n", "   'suffices IHf5: ~ exists x, exists2 p, pre_hom_ring x p /\\\\ ~~ rlink (last x p) x & fcard face (diskF (map h (x :: p))) <= (size (x :: p) == 5).',\n", "   'case=> x [p xHRp lep5]; set xp := x :: p.',\n", "   'have hp_ok: proper_ring (map h xp).',\n", "   'rewrite {}/xp; case: p xHRp {lep5} => [|y [|y1 p1]] //= [] [].',\n", "   \"by rewrite /scycle /= /rlink cfaceC bridge'Gm.\",\n", "   'case/andP=> xRy _ /allP/=/and3P[ac_x ac_y _] _; apply: contraNneq => ehx.',\n", "   'have ac_ex: edge x \\\\in ac by rewrite (closed_connect acF xRy).',\n", "   'rewrite -embed_functor // in ehx.',\n", "   'by rewrite -(cface_inj_embed ac_ex xRy ehx) /rlink e2c.',\n", "   'case: (IHf5); exists x, p => //.',\n", "   'rewrite /= -(size_map h) eqSS leqNgt; apply/negP => nt_dF.',\n", "   'have{xHRp} [[xRp p_ac URhp] Lp] := xHRp.',\n", "   'have hxFehp: cface (h x) (edge (h (last x p))).',\n", "   'by have[] := andP URhp; rewrite /= rcons_path last_map cfaceC => /andP[].',\n", "   'have ac_x: x \\\\in ac := p_ac x (mem_head x _).',\n", "   \"case/lastP Dp: p => [|p1 y]; first by rewrite Dp /= bridge'Gm in hxFehp.\",\n", "   'have Dy: y = last x p by rewrite Dp last_rcons.',\n", "   'have ac_y: y \\\\in ac by rewrite Dy p_ac ?mem_last.',\n", "   'have [ry x<PERSON>ry hry] := cface_h_ac ac_x hxFehp.',\n", "   'pose rp := rcons (rev (map edge (belast x p1))) ry.',\n", "   'pose rx := edge (last x p1); pose rxp := rx :: rp.',\n", "   'have Ehrp: map h rxp = rot 1 (rev_ring (map h xp)).',\n", "   'rewrite /xp lastI /rev_ring !map_rcons rev_rcons rot1_cons -hry.',\n", "   'rewrite Dp belast_rcons {}/rxp {}/rx {}/rp -rcons_cons.',\n", "   'rewrite -rev_rcons -map_rcons -lastI map_rcons map_rev -!map_comp.',\n", "   'congr (rcons (rev _) _); apply/eq_in_map => z pz.',\n", "   'move/allP: p_ac xRp; rewrite {}Dp; case/splitPl: pz => p2 p3 Lp2.',\n", "   'rewrite rcons_cat -cat_cons cat_path headI lastI Lp2 cat_rcons all_cat.',\n", "   'set z1 := head y p3 => /and4P[_ ac_z ac_z1 _] /and3P[_ zRz1 _].',\n", "   'by rewrite /= embed_functor // (closed_connect acF zRz1).',\n", "   'case: IHf5; exists rx, rp.',\n", "   'split; last first.',\n", "   'move: xRp; rewrite Dp rcons_path => /andP[_ p1Ry].',\n", "   'rewrite last_rcons (rlinkFr p1Ry); apply: contra Lp => ryRy.',\n", "   'have ac_ry: ry \\\\in ac by rewrite -(closed_connect acF xFry).',\n", "   'have ac_ery: edge ry \\\\in ac by rewrite (closed_connect acF ryRy).',\n", "   'have:= canLR e2m hry; rewrite -Dy -embed_functor // => Dhy.',\n", "   'by rewrite -(cface_inj_embed _ _ Dhy) // /rlink e2c cfaceC.',\n", "   'split=> [|z|]; last 1 first.',\n", "   'by rewrite Ehrp rot_scycle; apply: scycle_rev_ring.',\n", "   'move: xFry xRp; rewrite cfaceC {rxp Ehrp}/rx {}/rp {hry}Dp.',\n", "   'elim: p1 ry (x) => [|x3 p1 IHp] x1 x2 /= x1Fx2 /andP[x2Rx3 x3Rp].',\n", "   'by rewrite /rlink e2c cfaceC x1Fx2.',\n", "   'by rewrite rev_cons rcons_path last_rcons IHp // /rlink e2c cfaceC.',\n", "   'rewrite -rcons_cons mem_rcons -rev_rcons inE /= mem_rev -map_rcons -lastI.',\n", "   'rewrite -{2}[z]e2c (mem_map edgeI) => /predU1P[->|p1ez].',\n", "   'by rewrite -(closed_connect acF xFry).',\n", "   'move/allP: p_ac xRp; rewrite {}Dp; case/splitPl: p1ez => [p2 p3 Lp2].',\n", "   'rewrite rcons_cat cat_path Lp2 headI /= all_cat /= /rlink e2c.',\n", "   'by case/and4P=> _ _ ac_x3 _ /and3P[_ /(closed_connect acF)-> _].',\n", "   'have EhrpF: diskF (map h rxp) =i diskF (rev_ring (map h xp)).',\n", "   'by move=> z; rewrite Ehrp diskF_rot.',\n", "   'have <-: size (map h xp) = size rxp.',\n", "   'by rewrite size_map /= Dp !size_rcons size_rev size_map size_belast.',\n", "   'rewrite (eq_n_comp_r EhrpF) (eq_n_comp_r (diskF_rev_ring cexGm URhp hp_ok)).',\n", "   'apply: contraFT (Birkhoff cexGm _ URhp); last by rewrite size_map.',\n", "   'by rewrite -ltnNge => nt_dFC; apply/andP.',\n", "   'case=> x [p [xHRp Lp] triv_dF]; have [] := negP Lp.',\n", "   'apply: trivial_hom_ring => //.',\n", "   'do [move Dxp: (x :: p) => xp; case: eqP => // sz_xp_5] in triv_dF *.',\n", "   'set hxp := map h xp; rewrite leqn0; apply/exists_inP=> [[u /= Du dFu]].',\n", "   'have{xHRp} [xRp p_ac URhxp] := xHRp; rewrite Dxp -/hxp in p_ac URhxp.',\n", "   'have{Du dFu triv_dF} DdF v: (v \\\\in diskF hxp) = cface u v.',\n", "   'have dFcF := closed_connect (diskF_face hxp).',\n", "   'apply/idP/idP=> [dFv | /dFcF <- //]; apply/(rootP cfaceC)/esym/eqP.',\n", "   'move: triv_dF; rewrite [fcard _ _](cardD1 u) inE /= Du dFu ltnS leqn0.',\n", "   'move/pred0P/(_ (froot face v)); rewrite /= inE /= (roots_root cfaceC) /=.',\n", "   'by rewrite -(dFcF _ _ (connect_root _ _)) (eqP Du) dFv andbT => /negbFE.',\n", "   'pose ru := spoke_ring u; pose frf := froots (@face Gm).',\n", "   'have{frf} DxpF: fband hxp =i fband ru.',\n", "   'have ruF_xpF: [predI frf & fband ru] \\\\subset [predI frf & fband hxp].',\n", "   'apply/subsetP=> v1 /andP/=[Dv1 /hasP[v ru_v v1Fv]].',\n", "   'rewrite inE /= (closed_connect (fbandF hxp) v1Fv) {v1 v1Fv}Dv1 /=.',\n", "   'have{ru_v} uFnv: cface u (node v) by rewrite -mem_spoke_ring.',\n", "   'have dNnv: node v \\\\in diskN hxp by move: uFnv; rewrite -DdF => /andP[].',\n", "   'have: v \\\\notin diskF hxp.',\n", "   \"by rewrite DdF (same_cface uFnv) -{2}[v]nodeK -cface1r bridge'Gm.\",\n", "   'by rewrite inE /=; apply: contraR => ->; rewrite (fclosed1 (diskN_node _)).',\n", "   'have EpFr: fcard face (fband ru) = fcard face (fband hxp).',\n", "   'apply/eqP; rewrite eqn_leq subset_leq_card //.',\n", "   'rewrite (scycle_fcard_fband URhxp) size_map sz_xp_5.',\n", "   'rewrite (@scycle_fcard_fband Gm rlink); last exact: scycle_spoke_ring.',\n", "   'by rewrite size_spoke_ring pentaGm.',\n", "   'move=> v; have:= subset_cardP EpFr ruF_xpF (froot face v).',\n", "   'rewrite !inE (roots_root cfaceC) /=.',\n", "   'by rewrite -!(closed_connect (fbandF _) (connect_root _ v)).',\n", "   'have hxp_ru: {subset hxp <= ru}.',\n", "   'move=> v hxp_v; have [cycRhxp _] := andP URhxp.',\n", "   'have uAv: adj u v.',\n", "   'by rewrite -fband_spoke_ring -DxpF (subsetP (ring_fband _)).',\n", "   'have uAev: adj u (edge v).',\n", "   'rewrite (adjFr (next_cycle cycRhxp hxp_v)) -fband_spoke_ring -DxpF.',\n", "   'by rewrite (subsetP (ring_fband _)) ?mem_next.',\n", "   'have /orP[//|] := adj11_edge cexGm uAv uAev.',\n", "   'rewrite mem_spoke_ring -DdF => /andP[_] /=.',\n", "   'rewrite -(fclosed1 (diskN_node _)) diskN_edge_ring //.',\n", "   'by rewrite /hxp -(mkseq_nth x xp) sz_xp_5.',\n", "   'have: fcycle (face \\\\o face \\\\o edge) hxp.',\n", "   'have [[cycRhxp UhxpF] URu] := (andP URhxp, scycle_spoke_ring cexGm u).',\n", "   'apply cycle_from_next=> [|v hxp_v]; first exact: simple_uniq.',\n", "   'apply/eqP/(scycle_cface URu); first 2 [by rewrite hxp_ru ?mem_next].',\n", "   'by rewrite /= -(next_spoke_ring cexGm (hxp_ru v hxp_v)) mem_next hxp_ru.',\n", "   'by rewrite -!cface1; apply: next_cycle cycRhxp hxp_v.',\n", "   'rewrite /hxp -Dxp /= rcons_path last_map => /andP/=[hxFFEhp /eqP Lhp].',\n", "   'have nxp_nxF: map node xp \\\\subset cface (node x).',\n", "   'rewrite subset_all -Dxp /= inE connect0 /=.',\n", "   'move/allP: p_ac xRp hxFFEhp; rewrite -Dxp /=.',\n", "   'elim: (p) (x) => //= x2 r IHr x1 => /andP[ac_x1 r_ac] /andP[x1Rx2 x2Rp].',\n", "   'case/andP=> /eqP/esym Dhx2 hx2FFEhr; have [ac_x2 _] := andP r_ac.',\n", "   'suffices{r IHr r_ac x2Rp hx2FFEhr} nx1Fnx2: cface (node x1) (node x2).',\n", "   'by rewrite inE nx1Fnx2 (eq_all (same_cface nx1Fnx2)) IHr.',\n", "   'have ac_ex1: edge x1 \\\\in ac by rewrite (closed_connect acF x1Rx2).',\n", "   'rewrite -embed_functor // -!hF -?(fclosed1 acF) // in Dhx2.',\n", "   'have: cface x2 (face (face (edge x1))) by rewrite cfaceC -!cface1.',\n", "   'move/cface_inj_embed->; rewrite // -(canRL edgeK (e2c _)) cface1r.',\n", "   'by rewrite -{2}(n3c (ac_bc ac_x1)) !nodeK.',\n", "   \"have ac'nx: (node x \\\\notin ac).\",\n", "   'apply: contra Lp => ac_nx; set y := last x p in Lhp *; rewrite -Dxp in p_ac.',\n", "   'have ac_x: x \\\\in ac := p_ac x (mem_head x p).',\n", "   'have ac_enx: edge (node x) \\\\in ac by rewrite (fclosed1 acF) nodeK.',\n", "   'have ac_y: y \\\\in ac := p_ac y (mem_last x p).',\n", "   'have ac_eny: edge (node y) \\\\in ac by rewrite (fclosed1 acF) nodeK.',\n", "   'have nxFny: cface (node x) (node y).',\n", "   'by apply: (subsetP nxp_nxF); rewrite map_f // -Dxp mem_last.',\n", "   'have ac_ny: node y \\\\in ac by rewrite -(closed_connect acF nxFny).',\n", "   'have hny: h (node y) = h (face (node x)).',\n", "   'rewrite hF // -[h (node x)]edgeK -embed_functor // -hF // nodeK -Lhp.',\n", "   'rewrite -[h y]n3m -(canRL edgeK (e2m _)) !nodeK.',\n", "   'by rewrite -{2}[y]nodeK hF // embed_functor // edgeK.',\n", "   'have nyFfnx: cface (node y) (face (node x)) by rewrite cfaceC -cface1.',\n", "   'have bc_y: y \\\\in bc := ac_bc ac_y.',\n", "   'rewrite /rlink -(n3c bc_y) 2!cface1 nodeK -[node (node y)]e2c.',\n", "   'by rewrite (cface_inj_embed ac_ny nyFfnx) // faceK nodeK.',\n", "   'have nxp_ace: map node xp \\\\subset [predI cface (node x) & [preim edge of ac]].',\n", "   'apply/subsetP=> y nxp_y; rewrite inE /= (subsetP nxp_nxF) //= inE /=.',\n", "   'by have{y nxp_y} [z xp_z ->] := mapP nxp_y; rewrite (fclosed1 acF) nodeK p_ac.',\n", "   \"have:= leq_trans (subset_leq_card nxp_ace) (adj_kernel_max ac'nx).\",\n", "   'suffices /card_uniqP->: uniq (map node xp) by rewrite size_map sz_xp_5.',\n", "   'by rewrite (map_inj_uniq nodeI); have [_ /simple_uniq/map_uniq] := andP URhxp.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma pre_embed_inj : {in ac &, injective h}.',\n", "   'Proof.',\n", "   'move=> x y ac_x ac_y /= Dhx; have ac_eex: edge (edge x) \\\\in ac by rewrite e2c.',\n", "   'have [[|z1 [|z2 p]] [//=/andP[xFz1 z1Rp] Lp _] [_ p_ac]] := h_spoke ac_eex ac_y.',\n", "   'by apply: cface_inj_embed; rewrite // -[x]e2c (same_cface xFz1).',\n", "   'have [z1Rz2 _] := andP z1Rp; rewrite /rlink e2c in xFz1.',\n", "   'have [/andP[ac_z1 /eqP hez1] /andP[ac_z2 _] _] := and3P p_ac.',\n", "   'have hxFhz1 := cface_ac_h ac_x xFz1; rewrite Dhx in hxFhz1.',\n", "   'have [t yFt Dht] := cface_h_ac ac_y hxFhz1.',\n", "   'have ac_t: t \\\\in ac by rewrite -(closed_connect acF yFt).',\n", "   'have ac_ez1: edge z1 \\\\in ac by rewrite (closed_connect acF z1Rz2).',\n", "   'apply: cface_inj_embed; rewrite // (same_cface xFz1) cfaceC.',\n", "   'by rewrite -(edgeI (embed_full ac_t ac_ez1 _)) // Dht.',\n", "   'Qed.']},\n", " {'Definition': 'Definition embed x := if x \\\\in ac then h x else if edge x \\\\in ac then edge (h (edge x)) else if node x \\\\in ac then face (edge (h (node x))) else edge (node (node (h (node (edge x))))).'},\n", " {'Notation': 'Notation h1 := embed.'},\n", " {'Lemma': ['Lemma embed_cases x : (x \\\\in ac \\\\/ edge x \\\\in ac) \\\\/ node x \\\\in ac \\\\/ node (edge x) \\\\in ac.',\n", "   'Proof.',\n", "   'suffices{x} in_bc x: bc x -> (x \\\\in ac \\\\/ edge x \\\\in ac) \\\\/ node x \\\\in ac.',\n", "   'by case/orP: (edge_perimeter x) => /in_bc; rewrite ?e2c; tauto.',\n", "   'move=> bc_x; have [rc_ex | bc_ex] := boolP (edge x \\\\in rc); last first.',\n", "   'by case: (orP (chordless_perimeter bc_x bc_ex)); tauto.',\n", "   'have [rc_enx | bc_enx] := boolP (edge (node x) \\\\in rc); last first.',\n", "   'rewrite inE /= (fclosed1 rcN) in bc_x.',\n", "   'case: (orP (chordless_perimeter bc_x bc_enx)); first tauto.',\n", "   'by rewrite (fclosed1 acF) nodeK; tauto.',\n", "   'have rc_fx: face x \\\\in rc by rewrite (fclosed1 rcN) -(canRL edgeK (e2c x)).',\n", "   'have Dfx: face x = edge (node x).',\n", "   'by rewrite (scycle_cface UNrc rc_fx rc_enx) // -cface1 cface1r nodeK.',\n", "   'have EfxF: cface (face x) =i [:: x; face x].',\n", "   'by apply: fconnect_cycle; rewrite ?inE /= ?Dfx ?nodeK !eqxx ?orbT.',\n", "   'have:= allP (embeddable_ring embedGc) _ rc_fx.',\n", "   'rewrite -[good_ring_arity _]mem_seq4 (mem_iota 3 4).',\n", "   'by rewrite /order (eq_card EfxF) ltnNge card_size.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embedE : {morph h1 : x / edge x}.',\n", "   'Proof.',\n", "   'move=> x; wlog bc_x: x / x \\\\in bc.',\n", "   'move=> IH; case: (orP (edge_perimeter x)) => /IH //.',\n", "   'by rewrite e2c => ->; rewrite e2m.',\n", "   'rewrite /h1 e2c; do 2!case: ifPn; rewrite ?e2m //; first exact: embed_functor.',\n", "   \"move=> ac'x ac'ex; have [/orP/norP[]//|/orP] := embed_cases x.\",\n", "   \"case: ifPn => [ac_nex _ | ac'nex]; last first.\",\n", "   'by rewrite orbF (canRL nodeK (n3m _)) => ->.',\n", "   'have bc_ex: edge x \\\\in bc by rewrite inE /= (fclosed1 rcN); apply: ac_bc.',\n", "   'by have /norP[] := chordless_perimeter bc_x bc_ex.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embedN : {in bc, {morph h1 : x / node x}}.',\n", "   'Proof.',\n", "   'move=> x bc_x; rewrite /h1 (fclosed1 acF (edge (node x))) nodeK.',\n", "   'rewrite (canRL nodeK (n3c bc_x)) -(fclosed1 acF).',\n", "   \"have [ac_x | ac'x] := boolP (x \\\\in ac).\",\n", "   'have ac_enx: edge (node x) \\\\in ac by rewrite (fclosed1 acF) nodeK.',\n", "   'apply: edgeI; rewrite -[x as x in h x]nodeK (hF ac_enx) faceK fun_if e2m.',\n", "   'by case: ifP => // ac_nx; rewrite embed_functor.',\n", "   \"have [ac_ex | ac'ex] := boolP (edge x \\\\in ac).\",\n", "   \"case: ifPn => [ac_nx | ac'nx]; last first.\",\n", "   'by rewrite -(canRL nodeK (n3m _)) hF // (canRL edgeK (e2m _)).',\n", "   'rewrite (canRL edgeK (e2m _)) -hF // -(canRL nodeK (n3c bc_x)).',\n", "   'have ac_ennx: edge (node (node x)) \\\\in ac by rewrite (fclosed1 acF) nodeK.',\n", "   'rewrite (canRL nodeK (n3m _)) -embed_functor -?hF ?nodeK //.',\n", "   'by rewrite (canRL nodeK (n3c bc_x)) -fclosed1.',\n", "   'case: (embed_cases x) => /orP; first by case/norP.',\n", "   'rewrite (fun_if node) edgeK; case: ifP => //= _ ac_nex.',\n", "   'have bc_ex: edge x \\\\in bc by rewrite inE /= (fclosed1 rcN); apply: ac_bc.',\n", "   'by have /norP[] := chordless_perimeter bc_x bc_ex.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embed_inj : {in bc &, injective h1}.',\n", "   'Proof.',\n", "   'have bc_acN x: bc x -> x \\\\notin ac -> edge x \\\\notin ac -> node x \\\\in ac.',\n", "   \"move=> bc_x ac'x ac'ex; case: (embed_cases x) => [/orP/norP|] [] // ac_enx.\",\n", "   'have bc_ex: edge x \\\\in bc by rewrite inE /= (fclosed1 rcN) [~~ _]ac_bc.',\n", "   'by have /norP[] := chordless_perimeter bc_x bc_ex.',\n", "   'move=> x y; without loss ac_x: x y / x \\\\in ac.',\n", "   'move=> IH bc_x bc_y eq_hxy.',\n", "   \"have [ac_x | ac'x] := boolP (x \\\\in ac); first exact: IH.\",\n", "   \"have [ac_y | ac'y] := boolP (y \\\\in ac); first exact/esym/IH.\",\n", "   \"have [ac_nx | ac'nx] := boolP (node x \\\\in ac).\",\n", "   'by apply/nodeI/IH; rewrite ?embedN ?eq_hxy // inE /= -(fclosed1 rcN).',\n", "   \"have [ac_ny | ac'ny] := boolP (node y \\\\in ac).\",\n", "   'by apply/nodeI/esym/IH; rewrite ?embedN ?eq_hxy // inE /= -(fclosed1 rcN).',\n", "   'by apply/edgeI/IH; rewrite ?embedE ?eq_hxy ?ac_bc ?(contraR (bc_acN _ _ _)).',\n", "   'move=> bc_x bc_y; rewrite /h1 ac_x.',\n", "   \"have [|ac'y] := ifPn; first exact: pre_embed_inj.\",\n", "   \"have [ac_ey | ac'ey] := ifPn; first by move/esym/embed_full <-; rewrite ?e2c.\",\n", "   'have ac_ny: node y \\\\in ac by apply: bc_acN.',\n", "   'have ac_enx: edge (node x) \\\\in ac by rewrite (fclosed1 acF) nodeK.',\n", "   'rewrite bc_acN // -{1}[x]nodeK hF // => /faceI h_enx.',\n", "   'exact/esym/nodeI/edgeI/embed_full.',\n", "   'Qed.']},\n", " {'Local': 'Local Notation ddart := {x | x \\\\in bc}.'},\n", " {'Definition': 'Definition embd_edge x := if edge x \\\\in bc then edge x else edge (node (edge x)).'},\n", " {'Fact': ['Fact embd_edge_subproof (u : ddart) : embd_edge (sval u) \\\\in bc.',\n", "   'Proof.',\n", "   \"rewrite /embd_edge; case: ifPn => // bc'eu.\",\n", "   'have:= edge_perimeter (node (edge (val u))).',\n", "   \"by rewrite inE /= -(fclosed1 rcN) -implyNb bc'eu.\",\n", "   'Qed.']},\n", " {'Fact': ['Fact embd_node_subproof (u : ddart): node (sval u) \\\\in bc.',\n", "   'Proof.',\n", "   'by rewrite inE /= -(fclosed1 rcN) [~~ _](valP u).',\n", "   'Qed.']},\n", " {'Definition': 'Definition embd_face x := if face x \\\\in bc then face x else face (face x).'},\n", " {'Fact': ['Fact embd_face_subproof (u : ddart) : embd_face (sval u) \\\\in bc.',\n", "   'Proof.',\n", "   'rewrite /embd_face; case: ifP (edge_perimeter (face (val u))) => //= _.',\n", "   'by rewrite inE /= (canRL edgeK (e2c _)) -(fclosed1 rcN).',\n", "   'Qed.']},\n", " {'Let': 'Let dedge u : ddart := Sub (embd_edge (val u)) (embd_edge_subproof u).'},\n", " {'Let': 'Let dnode u : ddart := Sub (node (val u)) (embd_node_subproof u).'},\n", " {'Let': 'Let dface u : ddart := Sub (embd_face (val u)) (embd_face_subproof u).'},\n", " {'Fact': ['Fact embed_disk_subproof : cancel3 dedge dnode dface.',\n", "   'Proof.',\n", "   'move=> u; apply: val_inj; rewrite /= /embd_edge.',\n", "   \"case: ifP => [bc_ex | bc'ex]; last by rewrite /embd_face nodeK bc'ex edgeK.\",\n", "   'by rewrite /embd_face inE /= (fclosed1 rcN) edgeK [~~ _](valP u) edgeK.',\n", "   'Qed.']},\n", " {'Definition': 'Definition embed_disk := Hypermap embed_disk_subproof.'},\n", " {'Local': 'Local Notation Gd := embed_disk.'},\n", " {'Definition': 'Definition embd (u : Gd) := sval u.'},\n", " {'Lemma': ['Lemma embd_inj : injective embd.',\n", "   'Proof.',\n", "   'exact: val_inj.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma codom_embd : codom embd =i bc.',\n", "   'Proof.',\n", "   'move=> x; apply/imageP/idP => [[u _ ->] | bc_x]; first exact: (valP u).',\n", "   'by exists (Sub x bc_x).',\n", "   'Qed.']},\n", " {'Definition': 'Definition embd_ring : seq Gd := preim_seq embd erc.'},\n", " {'Lemma': ['Lemma map_embd_ring : map embd embd_ring = erc.',\n", "   'Proof.',\n", "   'apply: map_preim => _ /mapP[x rc_x ->]; rewrite codom_embd.',\n", "   'by have:= edge_perimeter x; rewrite inE /= rc_x.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma cface_embd : {mono embd : u v / cface u v}.',\n", "   'Proof.',\n", "   'move=> u v; apply/idP/idP=> /connectP[p]; last first.',\n", "   'move=> uFp -> {v}; elim: p u uFp => //= v p IHp u /andP[Dv /IHp].',\n", "   'apply: connect_trans; rewrite -(eqP Dv) /embd /= /embd_face.',\n", "   'by case: ifP => _; rewrite -!cface1r.',\n", "   'elim: {p}_.+1 u {-2}p (ltnSn (size p)) => // n IHn u [|y p] lepn.',\n", "   'by move=> _ Dv; apply/eq_connect0/embd_inj.',\n", "   'rewrite cface1 /= => /andP[/eqP Dy yFp] Lp.',\n", "   'have{IHn}:= IHn (face u); rewrite /= /embd_face Dy.',\n", "   \"have [bc_y | bc'y] := ifP; first by move/(_ p)->.\",\n", "   \"case: p => [|z p] /= in yFp Lp lepn *; first by rewrite -Lp (valP v) in bc'y.\",\n", "   'by case/andP: yFp => /eqP-> zFp; move/(_ p) => -> //; apply: ltnW.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma scycle_embd_ring : sfcycle edge embd_ring.',\n", "   'Proof.',\n", "   'have UbGdF: simple embd_ring.',\n", "   'move: (scycle_simple URerc); rewrite -map_embd_ring.',\n", "   'elim: embd_ring => //= u q IHq; rewrite !simple_cons !unfold_in has_map.',\n", "   'by rewrite (eq_has (cface_embd u)) => /andP[->].',\n", "   'rewrite /scycle UbGdF andbT; have [UbGd Iemb] := (simple_uniq UbGdF, embd_inj).',\n", "   'apply: cycle_from_next => //= u; rewrite -(mem_map Iemb) -(inj_eq Iemb).',\n", "   'rewrite -next_map // map_embd_ring /embd /= /embd_edge => /mapP[x rc_x ->{u}].',\n", "   'by rewrite e2c inE /= rc_x (next_map edgeI) //= (eqP (next_cycle cycNrc rc_x)).',\n", "   'Qed.']},\n", " {'Let': 'Let bGdE : fclosed edge embd_ring.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': \"Let bGd'E := predC_closed bGdE.\"},\n", " {'Definition': 'Definition embdd u := h1 (embd u).'},\n", " {'Lemma': ['Lemma embdd_inj : injective embdd.',\n", "   'Proof.',\n", "   'by move=> u v /(embed_inj (valP u) (valP v))/embd_inj.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embddE : {in [predC embd_ring], {morph embdd: u / edge u}}.',\n", "   'Proof.',\n", "   'move=> u; rewrite inE /= -(mem_map embd_inj) map_embd_ring -embedE.',\n", "   'by rewrite -{1}[embd u]e2c (mem_map edgeI) /embdd /= /embd_edge inE /= => ->.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embddN : {morph embdd: u / node u}.',\n", "   'Proof.',\n", "   'by move=> u; rewrite /embdd /= (embedN (valP u)).',\n", "   'Qed.']},\n", " {'Let': \"Let rdom' := image embdd [predC embd_ring].\"},\n", " {'Local': \"Local Notation rdart := {x | x \\\\notin rdom'}.\"},\n", " {'Fact': [\"Fact embr_edge_subproof (w : rdart) : edge (sval w) \\\\notin rdom'.\",\n", "   'Proof.',\n", "   \"apply: contra (valP w) => /= /imageP[u bGd'u /(canRL e2m)->{w}].\",\n", "   \"by rewrite -embddE // image_f // -(fclosed1 bGd'E).\",\n", "   'Qed.']},\n", " {'Let': 'Let bGd_proj x := [pred u in embd_ring | x == embdd u].'},\n", " {'Definition': 'Definition embr_node x := oapp (embdd \\\\o node \\\\o face) (node x) (pick (bGd_proj x)).'},\n", " {'Fact': [\"Fact embr_node_subproof (w : rdart) : embr_node (val w) \\\\notin rdom'.\",\n", "   'Proof.',\n", "   \"rewrite /embr_node; have [u /andP[bGd_u /eqP Dw] | bGd'w] := pickP.\",\n", "   \"by rewrite Dw /= (mem_image embdd_inj) (fclosed1 bGd'E) faceK negbK.\",\n", "   \"apply/imageP=> [] [u /= bGd'u ndw]; have /andP[] := bGd'w (node (node u)).\",\n", "   'have Dw: val w = embdd (node (node u)) by rewrite !embddN -ndw n3m.',\n", "   'by rewrite [_ \\\\in _](contraR _ (valP w)) ?Dw // (mem_image embdd_inj).',\n", "   'Qed.']},\n", " {'Definition': 'Definition embr_face x := oapp (embdd \\\\o edge) (face x) (pick (bGd_proj (edge x))).'},\n", " {'Fact': [\"Fact embr_face_subproof (w : rdart) : embr_face (val w) \\\\notin rdom'.\",\n", "   'Proof.',\n", "   \"rewrite /embr_face; have [u /andP[bGd_u /eqP/(canRL e2m) Dw] | bGd'ew] := pickP.\",\n", "   \"by rewrite Dw /= (mem_image embdd_inj) -(fclosed1 bGd'E) negbK.\",\n", "   \"apply/imageP=> [] [u /= bGd'u fdw]; have /andP[] := bGd'ew (node u).\",\n", "   'have Dew: edge (val w) = embdd (node u).',\n", "   'by rewrite (canRL edgeK (e2m _)) fdw embddN.',\n", "   'rewrite [_ \\\\in _](contraR _ (embr_edge_subproof w)) ?Dew //.',\n", "   'by rewrite (mem_image embdd_inj).',\n", "   'Qed.']},\n", " {'Let': 'Let redge w : rdart := Sub (edge (val w)) (embr_edge_subproof w).'},\n", " {'Let': 'Let rnode w : rdart := Sub (embr_node (val w)) (embr_node_subproof w).'},\n", " {'Let': 'Let rface w : rdart := Sub (embr_face (val w)) (embr_face_subproof w).'},\n", " {'Fact': ['Fact embed_rem_subproof : cancel3 redge rnode rface.',\n", "   'Proof.',\n", "   'move=> w; apply: val_inj; rewrite /= /embr_face e2m.',\n", "   \"have [u /andP[bGd_u /eqP->] | bGd'w] := pickP; rewrite /embr_node.\",\n", "   'have [_ /andP[_ /eqP/embdd_inj <-] | /(_ (edge u))] /= := pickP.',\n", "   'by rewrite [dnode _](edgeK u).',\n", "   'by rewrite -(fclosed1 bGdE) bGd_u eqxx.',\n", "   'have [u /andP[bGd_u /eqP/= Dfew] | _] /= := pickP; last exact: edgeK.',\n", "   'have /negP[]:= valP w; rewrite -[val w]edgeK Dfew -embddN image_f //.',\n", "   \"by apply: contraFN (bGd'w (node u)) => /= -> /=; rewrite embddN -Dfew edgeK.\",\n", "   'Qed.']},\n", " {'Definition': 'Definition embed_rem := Hypermap embed_rem_subproof.'},\n", " {'Local': 'Local Notation Gr := embed_rem.'},\n", " {'Definition': 'Definition embr (u : Gr) := val u.'},\n", " {'Lemma': ['Lemma embr_inj : injective embr.',\n", "   'Proof.',\n", "   'exact: val_inj.',\n", "   'Qed.']},\n", " {'Lemma': [\"Lemma codom_embr : codom embr =i [predC rdom'].\",\n", "   'Proof.',\n", "   \"move=> x; apply/imageP/idP=> [[w _ ->] | bG'x]; first exact: (valP w).\",\n", "   \"by exists (Sub x bG'x).\",\n", "   'Qed.']},\n", " {'Definition': 'Definition embr_ring : seq Gr := preim_seq embr (rev (map embdd embd_ring)).'},\n", " {'Lemma': ['Lemma map_embr_ring : map embr embr_ring = rev (map embdd embd_ring).',\n", "   'Proof.',\n", "   'apply: map_preim => x; rewrite codom_embr mem_rev => /mapP[u bGd_u ->{x}].',\n", "   'by rewrite inE /= (mem_image embdd_inj) negbK.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma ucycle_embr_ring : ufcycle node embr_ring.',\n", "   'Proof.',\n", "   'have [[cycEbGd /simple_uniq UbGd] Iembr] := (andP scycle_embd_ring, embr_inj).',\n", "   'have UbGm: uniq (map embdd embd_ring) by rewrite (map_inj_uniq embdd_inj).',\n", "   'have UbGr: uniq embr_ring.',\n", "   'by rewrite -(map_inj_uniq Iembr) map_embr_ring rev_uniq.',\n", "   'rewrite /ucycle UbGr andbT; apply: cycle_from_next => //= w.',\n", "   'rewrite -(inj_eq Iembr) -next_map // -(mem_map Iembr) map_embr_ring mem_rev.',\n", "   'rewrite next_rev /embr //= /embr_node => /mapP[u bGd_u ->].',\n", "   'rewrite (prev_map embdd_inj) // eq_sym.',\n", "   'have [_ /andP[_ /eqP/embdd_inj <-] | /(_ u)/andP[]//] := pickP.',\n", "   'by rewrite (inj_eq embdd_inj) -(canF_eq edgeK) [_ == _](prev_cycle cycEbGd).',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embed_patch : patch embdd embr embd_ring embr_ring.',\n", "   'Proof.',\n", "   'split.',\n", "   'exact: embdd_inj.',\n", "   'exact: embr_inj.',\n", "   'exact: scycle_embd_ring.',\n", "   'exact: ucycle_embr_ring.',\n", "   'exact: map_embr_ring.',\n", "   'move=> x; rewrite codom_embr !inE.',\n", "   'apply/imageP/norP=> [[u bGd_u ->] | [/negbNE/imageP[u _ ->]]].',\n", "   'by rewrite codom_f // (mem_map embdd_inj).',\n", "   'by rewrite (mem_map embdd_inj); exists u.',\n", "   'exact: embddE.',\n", "   'exact: embddN.',\n", "   'by [].',\n", "   'move=> w; rewrite inE /= -(mem_map embr_inj) map_embr_ring /embr /= /embr_node.',\n", "   'by case: pickP => //= u /andP[bGd_u /eqP->]; rewrite mem_rev map_f.',\n", "   'Qed.']},\n", " {'Local': 'Local Notation ppGm := embed_patch.'},\n", " {'Lemma': ['Lemma planar_embr : planar Gr.',\n", "   'Proof.',\n", "   'by have:= planarGm; rewrite /planar (genus_patch ppGm) addn_eq0 => /andP[].',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma plain_embr : plain Gr.',\n", "   'Proof.',\n", "   'by have:= plainGm; rewrite (plain_patch ppGm) => /andP[].',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma cubic_embr : quasicubic embr_ring.',\n", "   'Proof.',\n", "   'by have: cubic Gm := cexGm; rewrite (cubic_patch ppGm) => /andP[].',\n", "   'Qed.']},\n", " {'Notation': 'Notation ccr := (map h1 cc).'},\n", " {'Let': 'Let mapEh1 p : insertE (map h1 p) = map h1 (insertE p).'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let ccE x : x \\\\in insertE cc -> edge x \\\\in insertE cc.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Lemma': ['Lemma embed_sparse : sparse (insertE ccr).',\n", "   'Proof.',\n", "   \"move: Cred_cc => [[rc'cc UccN _ _] _]; move: rc'cc UccN.\",\n", "   'rewrite disjoint_has has_sym /sparse !simple_recI mapEh1 /=.',\n", "   \"elim: (insertE cc) => //= x p IHp /norP[rc'x rc'p] /andP[pN'x /IHp-> {IHp}//].\",\n", "   \"rewrite unfold_in has_map {pN'x}(contra _ pN'x) // => /hasP[z p_z /= h1xNz].\",\n", "   \"apply/hasP; exists z => //; have{p_z} rc'z := hasPn rc'p z p_z.\",\n", "   \"case/connectP: h1xNz => q; elim: q => /= [|y q IHq] in x rc'x *.\",\n", "   'by move=> _ /embed_inj->.',\n", "   \"case/andP=> /eqP <-; rewrite -(embedN rc'x) cnode1; apply: IHq.\",\n", "   'by rewrite -(fclosed1 rcN).',\n", "   'Qed.']},\n", " {'Let': 'Let cface_ac_h1 : {in ac, {mono h1 : x y / cface x y}}.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let cface_h1 : {homo h1 : x y / cface x y}.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let adj_ac_h1 : {in ac &, {mono h1 : x y / adj x y}}.'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Let': 'Let orbitF_h1 (x : Gc) : x \\\\in ac -> orbit face (h1 x) = map h1 (orbit face x).'},\n", " {'Proof': 'Proof.'},\n", " {'Qed': 'Qed.'},\n", " {'Lemma': ['Lemma embed_valid_contract : valid_contract [::] ccr.',\n", "   'Proof.',\n", "   'split; try exact: embed_sparse; rewrite ?disjoint_has // size_map ?mapEh1.',\n", "   'by have [[]] := Cred_cc.',\n", "   \"have [[rc'cc _ _ triad_cc] _] := Cred_cc; rewrite disjoint_has has_sym in rc'cc.\",\n", "   \"move=> {}/triad_cc/existsP[x /= /and3P[ac_x adj3x xA'cc]].\",\n", "   \"apply/existsP; exists (h1 x); apply/and3P; split=> // [{xA'cc} | {adj3x}].\",\n", "   'apply: leq_trans adj3x _; rewrite orbitF_h1 // /orbit.',\n", "   'move: (arity x) => n; elim: n => //= n IHn in x ac_x *.',\n", "   'apply: leq_add; last by rewrite IHn -?(fclosed1 acF).',\n", "   'rewrite -embedE /fband has_map; case: hasP => // [[y cc_y yFex]].',\n", "   'by case: hasP => // [[]]; exists y => //=; apply: cface_h1.',\n", "   \"apply: contra xA'cc => /subsetP ccAx; apply/subsetP=> y cc_y.\",\n", "   'have cc_ey: edge y \\\\in insertE cc by apply: ccE.',\n", "   'have {}ccAx z: z \\\\in insertE cc -> adj (h1 x) (h1 z).',\n", "   'by move=> cc_z; apply/ccAx/map_f.',\n", "   \"have bc_ey: edge y \\\\in bc by apply: (hasPn rc'cc).\",\n", "   'have: cface x (node y) || cface x (node (edge y)).',\n", "   \"rewrite -!(cface_ac_h1 ac_x) (embedN (hasPn rc'cc _ cc_y)) embedN //.\",\n", "   'by rewrite embedE -!mem_spoke_ring adj11_edge -?embedE ?ccAx.',\n", "   'case/orP=> [xFny | xFney].',\n", "   'by apply/adjP; exists (node y); rewrite // /rlink cface1 nodeK.',\n", "   'apply/adjP; exists (edge (face y)); last by rewrite /rlink e2c -cface1.',\n", "   'by rewrite -{1}[y]e2c -(n3c bc_ey) cface1r !nodeK.',\n", "   'Qed.']},\n", " {'Definition': 'Definition embed_cotrace := ring_trace (rotr 1 embr_ring).'},\n", " {'Lemma': ['Lemma embed_closure : Kempe_closed embed_cotrace.',\n", "   'Proof.',\n", "   'apply: Kemp<PERSON>_map; split; [ split | exact: planar_embr ].',\n", "   'split; first exact: plain_embr.',\n", "   'by apply/subsetP=> w; rewrite inE /= mem_rotr; apply: (subsetP cubic_embr w).',\n", "   'by rewrite rotr_ucycle; apply: ucycle_embr_ring.',\n", "   'Qed.']},\n", " {'Lemma': ['Lemma embed_contract : exists2 et, cc_ring_trace cc rrc et & embed_cotrace et.',\n", "   'Proof.',\n", "   \"have [[rc'cc _ _ _] _] := Cred_cc; rewrite disjoint_has has_sym in rc'cc.\",\n", "   'have [k [kE kF]] := contract_coloring cexGm embed_valid_contract.',\n", "   'pose k1 := k \\\\o h1; exists (trace (map k1 rrc)).',\n", "   'exists k1; split.',\n", "   'have k1Ebc x: x \\\\in bc -> invariant edge k1 x = (x \\\\in insertE cc).',\n", "   'move=> bc_x; rewrite /k1 /= embedE [_ == _]kE mapEh1.',\n", "   'apply/mapP/idP=> [[y c_y /embed_inj-> //]|]; last by exists x.',\n", "   \"exact: (hasPn rc'cc).\",\n", "   'move=> x; have /orP[|bc_ex] := edge_perimeter x; first exact: k1Ebc.',\n", "   'rewrite mem_insertE // (eq_has (cedge1 x)) -mem_insertE // -k1Ebc //= e2c.',\n", "   'by rewrite eq_sym.',\n", "   'move=> x; rewrite /k1 /=.',\n", "   'by rewrite -(fconnect_invariant kF (cface_h1 (fconnect1 face x))) eqxx.',\n", "   'exists (k \\\\o embr).',\n", "   'split=> w /=.',\n", "   'rewrite [_ == _]kE mapEh1; apply: contraNF (valP w) => /mapP[x cc_x /= ->].',\n", "   \"have xdP: x \\\\in codom embd by rewrite codom_embd; apply: (hasPn rc'cc).\",\n", "   'apply/imageP; exists (iinv xdP); rewrite /embdd ?f_iinv // inE /=.',\n", "   'rewrite -(mem_map embd_inj) map_embd_ring f_iinv -[x]e2c (mem_map edgeI).',\n", "   \"by apply: (hasPn rc'cc); apply: ccE.\",\n", "   'by apply/eqP/(fconnect_invariant kF); rewrite -(patch_face_r ppGm) -cface1.',\n", "   'rewrite !map_rotr 2!map_comp map_embr_ring (map_comp h1 embd) map_embd_ring.',\n", "   'rewrite !map_rev -rev_rot; congr (trace (rev _)).',\n", "   'case: rc cycNrc => //= x p; rewrite rot1_cons.',\n", "   'elim: p {1 3}x => [|_ p IHp] y /= /andP[/eqP <- nyNp].',\n", "   'congr [:: _]; apply/(fconnect_invariant kF)/cface_h1.',\n", "   'by rewrite cface1r nodeK.',\n", "   'congr (_ :: _); last exact: IHp.',\n", "   'by apply/(fconnect_invariant kF)/cface_h1; rewrite cface1r nodeK.',\n", "   'Qed.']},\n", " {'Theorem': ['Theorem not_embed_reducible : False.',\n", "   'Proof.',\n", "   'have [et etGc etGr] := embed_contract.',\n", "   'have{et etGc etGr}:= C_reducible_coclosure Cred_cc etGc embed_closure etGr.',\n", "   'case=> et [kc [kcE kcF] Detc] [kr col_kr Detr].',\n", "   'apply: minimal_counter_example_is_noncolorable (cexGm) _.',\n", "   'have [_] := colorable_patch ppGm; apply; exists (rev et); last first.',\n", "   'by rewrite revK Detr -trace_rot -!map_rot rotrK; exists kr.',\n", "   'exists (kc \\\\o embd).',\n", "   'split=> u.',\n", "   'rewrite /= /embd_edge; case: ifP => _; first exact: kcE.',\n", "   'by rewrite -(eqcP (kcF _)) nodeK; apply: kcE.',\n", "   'by apply/eqP/(fconnect_invariant kcF); rewrite cface_embd -cface1.',\n", "   'rewrite map_comp map_embd_ring Detc map_rev trace_rev rev_rot revK.',\n", "   'suffices Drc: rc = map face (rot 1 erc).',\n", "   'rewrite [in LHS]Drc -map_comp map_rot trace_rot rotK.',\n", "   'by congr (trace _); apply: eq_map => x; apply/eqP/kcF.',\n", "   'case: rc UNrc => // x p /andP[/=xNpx _]; rewrite rot1_cons.',\n", "   'by elim: p {1 3}x xNpx => /= [|y p IHp] z /andP[/eqP/(canRL nodeK)-> // /IHp->].',\n", "   'Qed.']}]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["list(data.values())[57]"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}