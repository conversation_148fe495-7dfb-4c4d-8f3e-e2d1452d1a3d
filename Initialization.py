from dataclasses import dataclass, field
from subprocess import check_output
import coq.queries as queries
import coq.sentences as sentences
from coq.serialization import json_dump
from coq.xmlInterface import Goals
from pathlib import Path
import re
from loguru import logger
import networkx as nx
import matplotlib.pyplot as plt

TIMEOUT = 30

@dataclass
class Step:
    goals: Goals
    # when seeing (the context and goal), one should proceed with the tactic
    tactic: str = ""
    premises: list[queries.AboutResult] = field(default_factory=lambda: [])

    # real location in the buffer before applying this step
    loc_line: int = 0
    loc_col: int = 0


@dataclass
class Theorem:
    kind: str = ""
    name: str = ""
    definition: str = ""
    steps: list[Step] = field(default_factory=lambda: [])
    cmds: list[str] = field(default_factory=lambda: [])
    
@dataclass
class Premise:
    keyword: str = ""
    cmd: str = ""

@dataclass 
# without proof steps yet
class Proof:
    premises: list[Premise] = field(default_factory=lambda: [])
    definition: str = ''

@dataclass
class vPath:
    logical: str
    physical: Path

@dataclass 
class CoqImport:
    path:str
    type:str # type \in {'import' 'export'}

class Projectinfo:
    def __init__(self,
                 project_path:Path,
                 logical_path:str) -> None:
        
        self.dependency : dict[str,list[CoqImport]] = {}
        self.path = vPath(physical = project_path, 
                          logical = logical_path)
        self.files: dict[str, list[sentences.Sentence]] = {} # filename : [vernaccmds]
        self.files_wo_proof: dict[str, list[sentences.Sentence]] = {} # filename : [vernaccmds wo proof]
        self.premises:dict[str, list[dict[str,str]]] = {} # filname : [{filename : premise}]
        self.load_info()

    def locate_v_files(self)->list[vPath]:
        base_path = Path(self.path.physical).resolve()      # 物理路径根目录
        logical_root = Path(self.path.logical).resolve()    # 逻辑路径对应的物理根目录

        results = []
        for path in base_path.rglob("*.v"):
            if path.is_file():
                abs_path = str(path.resolve())

                try: # get logical path
                    rel_to_logical = path.resolve().relative_to(logical_root)
                    logical = rel_to_logical.with_suffix('').as_posix().replace('/', '.')
                except ValueError:
                    logical = ''

                results.append(vPath(
                    physical = Path(abs_path), logical = logical
                ))

        return results

    def load_info(self):
        self.load_vernac_cmds()
        self.get_dependency()
        self.get_premises()

    # loading all cmds(w/wo proof) from all *.v
    def load_vernac_cmds(self) -> None:
        vfiles = self.locate_v_files()
        cmds = []
        filepaths = []
        
        for filename in vfiles:
            lines = open(filename.physical, "rb").readlines()
            vernac_cmds = sentences.split_sentences(lines)
            cmds.append(vernac_cmds)

            filepaths.append(filename.physical.as_posix())

        self.files = dict(zip(filepaths, cmds))
        for filename, vernac_cmds in self.files.items():
            cmds_wo_proof = []
            inside_proof = False
            for vernac_cmd in vernac_cmds:
                # Only "Proof." is required
                if ParsingCmds.is_proof_start(vernac_cmd):
                    inside_proof = True
                    cmds_wo_proof.append(vernac_cmd)
                    continue
                if ParsingCmds.is_proof_end(vernac_cmd):
                    inside_proof = False
                    continue
                if not inside_proof:
                    cmds_wo_proof.append(vernac_cmd)
            self.files_wo_proof[filename] = cmds_wo_proof
        # {str(Path) : cmds}

    def resolve_logical_path(self,
                             logical_prefix:str, # Coq
                             module:str # Coq.Strings.String 
        )->Path:
        assert module.startswith(logical_prefix + ".")
        
        relative_parts = module[len(logical_prefix) + 1:].split(".")
        return (self.path.physical / Path(*relative_parts).with_suffix(".v")).resolve()
            
        
    # dependency DAG of current project
    def get_dependency(self):
        if self.files == {}:
            self.load_vernac_cmds()

        for path, vernac_cmds in self.files.items():
            fileImports = []
            # require = 
            # {'proj': 'Coq', 
            # 'logical': 'Coq.Bool', 
            # 'type': 'Import', 
            # 'origin': 'From Coq Require Import Bool.'}
            for require in ParsingCmds.match_require(vernac_cmds):
                proj, module, type, origin = require.values()
                if proj != self.path.logical:
                    continue
                coqimport = CoqImport(self.resolve_logical_path(proj, module).as_posix(), # get physical path
                                      type)
                fileImports.append(coqimport)

            self.dependency[path] = fileImports

    def get_premises(self) -> None:
        for filename in self.dependency.keys(): # *.v file in the project
            vernac_cmds = self.files[filename]
            premises = ParsingCmds.extractPremises(vernac_cmds)
            self.premises[filename] = premises

    # show dependency DAG
    def plot_dependency(self, figurepath: str = "dependency.png"):
        dependency = self.dependency

        G = nx.DiGraph()
        for mod, imports in dependency.items():
            for imp in imports:
                G.add_edge(mod, imp.path, label=imp.type)

        pos = nx.spring_layout(G, seed=42)
        plt.figure(figsize=(12, 8))
        nx.draw(G, pos, with_labels=True, node_color='lightblue', node_size=1500, font_size=10, arrows=True)
        edge_labels = nx.get_edge_attributes(G, 'label')
        nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=9)

        plt.tight_layout()
        plt.savefig(figurepath, format="png", dpi=300)
        plt.close()
        logger.info(f"Dependency graph saved as '{figurepath}'")


    def save_premises(self, filepath = 'premises.json'):
        json_dump(self.premises, filepath)

        

class ParsingCmds:
    @staticmethod
    def match_require(vernac_cmds: list[sentences.Sentence]):
        # locate required *.v files
        results = []
        for vernac_cmd in vernac_cmds:
            cmd = vernac_cmd.text.strip()
            match1 = re.match(r"Require\s+(Import|Export)\s+([a-zA-Z0-9_.]+)", cmd.rstrip('.'))
            if match1:
                logger.info(f'detacted ||{cmd}||')
                import_type = match1.group(1)
                module = match1.group(2)
                results.append({
                    'proj':None, # physical path is invisible yet.
                    "logical": module,
                    "type": import_type,
                    "origin": cmd,
                })
                continue

            # From A.B Require Import/Export X.Y.Z
            match2 = re.match(r"From\s+([a-zA-Z0-9_.]+)\s+Require\s+(Import|Export)\s+([a-zA-Z0-9_.]+)", cmd.rstrip('.'))
            if match2:
                from_part = match2.group(1)
                import_type = match2.group(2)
                module_part = match2.group(3)
                full_module = f"{from_part}.{module_part}"
                results.append({
                    "proj": from_part,
                    'logical':full_module,
                    "type": import_type,
                    "origin": cmd,
                })
                continue
        return results
    

    @staticmethod
    # get structured data from imported files !! unfinished
    def getImportedFile(path_dict, importedfiles : list[dict]):
        for importmodel in importedfiles:
            logipath = importmodel['logical']
            assert logipath in path_dict, logger.info(f'Model {logipath} not found in LoadPath.')

    @staticmethod
    # check if cmd is start of a proof
    def is_proof_start(vernac_cmd):
        return vernac_cmd.text.startswith("Proof.")

    @staticmethod
    # check if cmd is end of a proof
    def is_proof_end(vernac_cmd):
        return vernac_cmd.text.startswith("Qed.") or vernac_cmd.text.startswith("Admitted.")

    @staticmethod
    def extractPremises(vernac_cmds):
        keywords = [
            # 定义类
            "Definition", "Fixpoint", "CoFixpoint",
            "Inductive", "CoInductive",
            "Record", "Structure", "Variant",
            "Let", "Module", "Module Type", "Section", "Variable", "Parameters",
            "Class", "Instance", "Canonical Structure", "Program Definition",

            # 证明类
            "Lemma", "Theorem", "Example", "Fact", "Corollary", "Proposition",
            "Goal", "Remark", "Proof", "Qed", "Admitted", "Defined",
            "Program Theorem", "Program Lemma",
            "Axiom", "Conjecture", "Hypothesis",

            # 语法和上下文
            "Notation", "Infix", "Arguments", "Import", "Export", "Open Scope",
            "Close Scope", "Bind Scope", "Delimit Scope", "Unset", "Set", "Context",
            "Local", "Global", "Hint", "Hint Resolve", "Hint Rewrite", "Hint Extern",
            "Obligation", "Require", "Include", "Existing Class", "Existing Instance"
        ]

        proof_start_keywords = [
            "Lemma", "Theorem", "Example", "Fact", "Corollary", "Proposition",
            "Goal", "Remark", "Program Theorem", "Program Lemma"
        ]

        proof_end_keywords = ["Qed", "Admitted", "Defined"]

        ignore_keywords = ["Require", "Import", "Export"]

        results = []
        inside_proof = False
        current_proof_block = []
        current_proof_kind = None

        for vernac_cmd in vernac_cmds:
            line = vernac_cmd.text.strip()
            if not line.endswith('.'): # like：- 
                continue

            if inside_proof: # proof
                current_proof_block.append(line)
                if any(line.startswith(kw) for kw in proof_end_keywords):
                    results.append({current_proof_kind: current_proof_block})
                    current_proof_block = []
                    current_proof_kind = None
                    inside_proof = False
                continue

            if any(line.startswith(kw) for kw in ignore_keywords): # ignore
                continue

            for kw in proof_start_keywords: # statement of a proof
                if line.startswith(kw):
                    inside_proof = True
                    current_proof_block = [line]
                    current_proof_kind = kw
                    break
            else:
                # non-proof mode cmd
                for kw in keywords:
                    if kw == 'Admitted': # some Definition or Fixpoint are not finished.
                        continue
                    if line.startswith(kw):
                        results.append({kw: line})
                        break

        return results






# if __name__ == "__main__":
#     parser = parser = argparse.ArgumentParser(description="Run Coq source file with configuration.")
#     parser.add_argument("src", type=str, help="Path to the Coq source file (.v)")
#     args = parser.parse_args()
    
#     top = coqtop.Coqtop()
#     filename = args.src
#     coqlib = runner.load_coq_project(Path(filename))

    
#     logger.info(f"working on [{filename}] with arguments {coqlib}")
#     lines = open(filename, "rb").readlines()
#     steps = sentences.split_sentences(lines)

#     version = top.find_coq(None, None)
#     logger.info(f"using coq version: {version}")

#     [err, msg] = top.start(
#         filename=filename,
#         coqproject_args=coqlib,
#         use_dune=False,
#         dune_compile_deps=False,
#         timeout=TIMEOUT,
#         stderr_is_warning=True,
#     )
#     logger.info("coqtop start message", msg)
#     assert err is None

#     # get logical path & physical path
#     path_dict = top.get_loadpath()
#     print(* path_dict.items(),sep='\n')
    
    # premises = Interact.extrcated_premises_file(steps = steps)
    # print(*premises, sep='\n')


    # proofs = Interact.extract_proofs(steps=steps, top = top)
    # print(proofs[0])



'''def extract_proofs(self, steps:list[sentences.Sentence], 
                              top:coqtop.Coqtop,
                              ifsave:bool = False):
        
        
        thm: Theorem | None = None
        theorems: list[Theorem] = []


        for sentence in steps:
            cmd = sentence.text
            line, col = sentence.line, sentence.col

            _, _, before_state, _ = top.goals()
            ok, _, _, err = top.advance(cmd, in_script=True)
            # assert ok
            _, _, after_state, _ = top.goals()
            


            # state transition: start proving a theorem
            if before_state is None and after_state is not None:
                
                
                if not thm:
                    thm = Theorem()
                    try:
                        thm.kind, thm.name, thm.definition = sentences.proof_meta(cmd)
                        print("+ working on", thm.kind, thm.name, thm.definition)
                    except Exception as _:
                        thm = None
            else:
                # update theorem recording
                if thm:
                    thm.cmds.append(cmd)  # record the raw commands

                    assert before_state is not None
                    thm.steps.append(
                        Step(
                            goals=before_state,  # the state on which the tactic is applied
                            tactic=cmd,  # the tactic command
                            premises=self.find_dependent_names(cmd),  # dependencies of this command
                            loc_line=line,
                            loc_col=col,
                        )
                    )

                    # state transition: theorem proved
                    if after_state is None:
                        if "Abort" not in cmd:
                            # we will not import aborted proofs
                            theorems.append(thm)
                        thm = None
                        print("- done with last theorem")

        
        
        @staticmethod
    def find_dependent_names(cmd: str) -> list[queries.AboutResult]:
        blacklist_names: set[str] = set(
            ["intro", "intros", "unfold", "simpl", "induction",
            "destruct", "discriminate", "contradiction", "split", "left",
            "right", "apply", "assumption", "eapply", "exact",
            "inversion", "injection", "f_equal", "rewrite", "change",
            "assert", "auto", "trivial", "intuition", "eauto",
            "reflexivity", "symmetry", "transitivity"
            ])
        dep_kinds: set[str] = set(
                [
                    "Constant",
                    "Constructor",
                ]
        )
        deps: list[queries.AboutResult] = []
        qualids = sentences.qualids(cmd)
        for name in qualids:
            if name in blacklist_names:
                continue
            about = queries.about(top, name)
            if about is not None and about.kind in dep_kinds:
                deps.append(about)

        return deps
        
        
        @staticmethod
    # !! not finished 
    '''