COQAUX1 f98551d55dd2417287034b28914fb041 /media/llm4itp/ssd1/ytchen/coq-python/plf/Imp.v
0 0 VernacProof "tac:no using:no"
5003 5007 proof_build_time "0.000"
0 0 test_aeval1 "0.000"
4990 5002 context_used ""
5003 5007 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
6534 6538 proof_build_time "0.000"
0 0 test_optimize_0plus "0.000"
6521 6533 context_used ""
6534 6538 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
7502 7506 proof_build_time "0.009"
0 0 optimize_0plus_sound "0.009"
7488 7500 context_used ""
7502 7506 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
9130 9134 proof_build_time "0.000"
0 0 silly1 "0.000"
9065 9074 context_used ""
9130 9134 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
9249 9253 proof_build_time "0.000"
0 0 silly2 "0.000"
9196 9212 context_used ""
9249 9253 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
10041 10045 proof_build_time "0.001"
0 0 foo "0.001"
10028 10040 context_used ""
10041 10045 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
10335 10339 proof_build_time "0.001"
0 0 foo' "0.001"
10200 10334 context_used ""
10335 10339 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
11312 11316 proof_build_time "0.007"
0 0 optimize_0plus_sound' "0.007"
11251 11309 context_used ""
11312 11316 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
13908 13912 proof_build_time "0.006"
0 0 optimize_0plus_sound'' "0.006"
13856 13907 context_used ""
13908 13912 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
14961 14965 proof_build_time "0.004"
0 0 In10 "0.004"
14920 14960 context_used ""
14961 14965 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
15301 15305 proof_build_time "0.004"
0 0 In10' "0.004"
15260 15300 context_used ""
15301 15305 proof_check_time "0.003"
0 0 VernacProof "tac:no using:no"
15820 15829 proof_build_time "0.000"
0 0 repeat_loop "0.000"
15820 15829 proof_check_time "0.000"
16915 16924 proof_build_time "0.000"
0 0 optimize_0plus_b "0.000"
16915 16924 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
17104 17113 proof_build_time "0.000"
0 0 optimize_0plus_b_test1 "0.000"
17104 17113 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
17305 17314 proof_build_time "0.000"
0 0 optimize_0plus_b_test2 "0.000"
17305 17314 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
17427 17436 proof_build_time "0.000"
0 0 optimize_0plus_b_sound "0.000"
17427 17436 proof_check_time "0.000"
19759 19763 proof_build_time "0.001"
0 0 invert_example1 "0.001"
19746 19758 context_used ""
19759 19763 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
20658 20662 proof_build_time "0.002"
0 0 silly_presburger_example "0.002"
20653 20657 context_used ""
20658 20662 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
20741 20745 proof_build_time "0.001"
0 0 add_comm__lia "0.001"
20736 20740 context_used ""
20741 20745 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
20837 20841 proof_build_time "0.001"
0 0 add_assoc__lia "0.001"
20832 20836 context_used ""
20837 20841 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
29553 29557 proof_build_time "0.007"
0 0 aevalR_iff_aeval "0.007"
29540 29552 context_used ""
29553 29557 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
29956 29960 proof_build_time "0.037"
0 0 aevalR_iff_aeval' "0.037"
29856 29955 context_used ""
29956 29960 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
30396 30405 proof_build_time "0.000"
0 0 bevalR_iff_beval "0.000"
30396 30405 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
42075 42079 proof_build_time "0.000"
0 0 aexp1 "0.000"
42062 42074 context_used ""
42075 42079 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
42172 42176 proof_build_time "0.000"
0 0 aexp2 "0.000"
42159 42171 context_used ""
42172 42176 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
42268 42272 proof_build_time "0.000"
0 0 bexp1 "0.000"
42255 42267 context_used ""
42268 42272 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
55038 55042 proof_build_time "0.001"
0 0 ceval_example1 "0.001"
55025 55037 context_used ""
55038 55042 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
55239 55248 proof_build_time "0.000"
0 0 ceval_example2 "0.000"
55239 55248 proof_check_time "0.000"
55786 55795 proof_build_time "0.000"
0 0 pup_to_n "0.000"
55786 55795 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
55945 55954 proof_build_time "0.000"
0 0 pup_to_2_ceval "0.000"
55945 55954 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
57693 57697 proof_build_time "0.034"
0 0 ceval_deterministic "0.034"
57680 57691 context_used ""
57693 57697 proof_check_time "0.008"
0 0 VernacProof "tac:no using:no"
58544 58548 proof_build_time "0.003"
0 0 plus2_spec "0.003"
58524 58542 context_used ""
58544 58548 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
59331 59340 proof_build_time "0.000"
0 0 loop_never_stops "0.000"
59331 59340 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
60189 60198 proof_build_time "0.000"
0 0 no_whiles_eqv "0.000"
60189 60198 proof_check_time "0.000"
63170 63179 proof_build_time "0.000"
0 0 s_execute "0.000"
63170 63179 proof_check_time "0.000"
63322 63331 proof_build_time "0.000"
0 0 s_execute1 "0.000"
63322 63331 proof_check_time "0.000"
63458 63467 proof_build_time "0.000"
0 0 s_execute2 "0.000"
63458 63467 proof_check_time "0.000"
63769 63778 proof_build_time "0.000"
0 0 s_compile "0.000"
63769 63778 proof_check_time "0.000"
63987 63996 proof_build_time "0.000"
0 0 s_compile1 "0.000"
63987 63996 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
64430 64439 proof_build_time "0.000"
0 0 execute_app "0.000"
64430 64439 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
64911 64920 proof_build_time "0.000"
0 0 s_compile_correct_aux "0.000"
64911 64920 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
65132 65141 proof_build_time "0.000"
0 0 s_compile_correct "0.000"
65132 65141 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
71218 71227 proof_build_time "0.000"
0 0 break_ignore "0.000"
71218 71227 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
71360 71369 proof_build_time "0.000"
0 0 while_continue "0.000"
71360 71369 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
71548 71557 proof_build_time "0.000"
0 0 while_stops_on_break "0.000"
71548 71557 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
71744 71753 proof_build_time "0.000"
0 0 seq_continue "0.000"
71744 71753 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
71898 71907 proof_build_time "0.000"
0 0 seq_stops_on_break "0.000"
71898 71907 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
72175 72184 proof_build_time "0.000"
0 0 while_break_true "0.000"
72175 72184 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
72443 72452 proof_build_time "0.000"
0 0 ceval_deterministic "0.000"
72443 72452 proof_check_time "0.000"
0 0 vo_compile_time "0.395"
