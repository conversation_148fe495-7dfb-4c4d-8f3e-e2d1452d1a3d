COQAUX1 60c3144fb322f351bba59837a5d44c95 /media/llm4itp/ssd1/ytchen/coq-python/plf/Equiv.v
0 0 VernacProof "tac:no using:no"
4875 4879 proof_build_time "0.002"
0 0 aequiv_example "0.002"
4870 4874 context_used ""
4875 4879 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
5023 5027 proof_build_time "0.014"
0 0 bequiv_example "0.014"
5010 5022 context_used ""
5023 5027 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
6669 6673 proof_build_time "0.008"
0 0 skip_left "0.008"
6657 6668 context_used ""
6669 6673 proof_check_time "0.003"
0 0 VernacProof "tac:no using:no"
6908 6917 proof_build_time "0.000"
0 0 skip_right "0.000"
6908 6917 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
7293 7297 proof_build_time "0.007"
0 0 if_true_simple "0.007"
7280 7291 context_used ""
7293 7297 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
9664 9668 proof_build_time "0.008"
0 0 if_true "0.008"
9654 9663 context_used ""
9664 9668 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
9879 9888 proof_build_time "0.000"
0 0 if_false "0.000"
9879 9888 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
10201 10210 proof_build_time "0.000"
0 0 swap_if_branches "0.000"
10201 10210 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
10915 10919 proof_build_time "0.056"
0 0 while_false "0.056"
10904 10913 context_used ""
10915 10919 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
12957 12961 proof_build_time "0.004"
0 0 while_true_nonterm "0.004"
12943 12955 context_used ""
12957 12961 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
13461 13470 proof_build_time "0.000"
0 0 while_true "0.000"
13461 13470 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
14520 14524 proof_build_time "0.012"
0 0 loop_unrolling "0.012"
14507 14518 context_used ""
14520 14524 proof_check_time "0.004"
0 0 VernacProof "tac:no using:no"
14693 14702 proof_build_time "0.000"
0 0 seq_assoc "0.000"
14693 14702 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
15272 15276 proof_build_time "0.005"
0 0 identity_assignment "0.005"
15262 15271 context_used ""
15272 15276 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
15493 15502 proof_build_time "0.000"
0 0 assign_aequiv "0.000"
15493 15502 proof_check_time "0.000"
16917 16926 proof_build_time "0.000"
0 0 equiv_classes "0.000"
16917 16926 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
17668 17672 proof_build_time "0.000"
0 0 refl_aequiv "0.000"
17654 17666 context_used ""
17668 17672 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
17804 17808 proof_build_time "0.000"
0 0 sym_aequiv "0.000"
17794 17802 context_used ""
17804 17808 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
18009 18013 proof_build_time "0.001"
0 0 trans_aequiv "0.001"
17995 18007 context_used ""
18009 18013 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
18119 18123 proof_build_time "0.000"
0 0 refl_bequiv "0.000"
18105 18117 context_used ""
18119 18123 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
18270 18274 proof_build_time "0.000"
0 0 sym_bequiv "0.000"
18260 18268 context_used ""
18270 18274 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
18475 18479 proof_build_time "0.001"
0 0 trans_bequiv "0.001"
18461 18473 context_used ""
18475 18479 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
18588 18592 proof_build_time "0.000"
0 0 refl_cequiv "0.000"
18574 18586 context_used ""
18588 18592 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
18740 18744 proof_build_time "0.001"
0 0 sym_cequiv "0.001"
18727 18739 context_used ""
18740 18744 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
18922 18926 proof_build_time "0.001"
0 0 trans_cequiv "0.001"
18911 18921 context_used ""
18922 18926 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
20630 20634 proof_build_time "0.031"
0 0 CAsgn_congruence "0.031"
20616 20628 context_used ""
20630 20634 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
23546 23550 proof_build_time "0.006"
0 0 CWhile_congruence "0.006"
23534 23545 context_used ""
23546 23550 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
23768 23777 proof_build_time "0.000"
0 0 CSeq_congruence "0.000"
23768 23777 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
24061 24070 proof_build_time "0.000"
0 0 CIf_congruence "0.000"
24061 24070 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
24673 24677 proof_build_time "0.001"
0 0 congruence_example "0.001"
24654 24672 context_used ""
24673 24677 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
27065 27069 proof_build_time "0.000"
0 0 fold_aexp_ex1 "0.000"
27052 27064 context_used ""
27065 27069 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
27609 27613 proof_build_time "0.000"
0 0 fold_aexp_ex2 "0.000"
27596 27608 context_used ""
27609 27613 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
29518 29522 proof_build_time "0.000"
0 0 fold_bexp_ex1 "0.000"
29505 29517 context_used ""
29518 29522 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
29654 29658 proof_build_time "0.000"
0 0 fold_bexp_ex2 "0.000"
29641 29653 context_used ""
29654 29658 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
31029 31033 proof_build_time "0.000"
0 0 fold_com_ex1 "0.000"
31016 31028 context_used ""
31029 31033 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
31917 31921 proof_build_time "0.027"
0 0 fold_constants_aexp_sound "0.027"
31402 31916 context_used ""
31917 31921 proof_check_time "0.011"
0 0 VernacProof "tac:no using:no"
36582 36591 proof_build_time "0.022"
0 0 fold_constants_bexp_sound "0.022"
36582 36591 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
37636 37645 proof_build_time "0.004"
0 0 fold_constants_com_sound "0.004"
37636 37645 proof_check_time "0.000"
38774 38783 proof_build_time "0.000"
0 0 optimize_0plus_aexp "0.000"
38774 38783 proof_check_time "0.000"
38889 38898 proof_build_time "0.000"
0 0 optimize_0plus_bexp "0.000"
38889 38898 proof_check_time "0.000"
39001 39010 proof_build_time "0.000"
0 0 optimize_0plus_com "0.000"
39001 39010 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
39185 39194 proof_build_time "0.000"
0 0 test_optimize_0plus "0.000"
39185 39194 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
39499 39508 proof_build_time "0.000"
0 0 optimize_0plus_aexp_sound "0.000"
39499 39508 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
39610 39619 proof_build_time "0.000"
0 0 optimize_0plus_bexp_sound "0.000"
39610 39619 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
39719 39728 proof_build_time "0.000"
0 0 optimize_0plus_com_sound "0.000"
39719 39728 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
40123 40132 proof_build_time "0.000"
0 0 optimizer_sound "0.000"
40123 40132 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
41631 41635 proof_build_time "0.001"
0 0 subst_aexp_ex "0.001"
41618 41630 context_used ""
41631 41635 proof_check_time "0.001"
0 0 VernacProof "tac:no using:no"
43394 43398 proof_build_time "0.007"
0 0 subst_inequiv "0.007"
43380 43393 context_used ""
43394 43398 proof_check_time "0.002"
0 0 VernacProof "tac:no using:no"
44488 44497 proof_build_time "0.000"
0 0 aeval_weakening "0.000"
44488 44497 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
44861 44870 proof_build_time "0.000"
0 0 inequiv_exercise "0.000"
44861 44870 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
49543 49552 proof_build_time "0.000"
0 0 havoc_example1 "0.000"
49543 49552 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
49649 49658 proof_build_time "0.000"
0 0 havoc_example2 "0.000"
49649 49658 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
50653 50662 proof_build_time "0.000"
0 0 pXY_cequiv_pYX "0.000"
50653 50662 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
51142 51151 proof_build_time "0.000"
0 0 ptwice_cequiv_pcopy "0.000"
51142 51151 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
52321 52330 proof_build_time "0.000"
0 0 p1_may_diverge "0.000"
52321 52330 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
52431 52440 proof_build_time "0.000"
0 0 p2_may_diverge "0.000"
52431 52440 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
52652 52661 proof_build_time "0.000"
0 0 p1_p2_equiv "0.000"
52652 52661 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
53101 53110 proof_build_time "0.000"
0 0 p3_p4_inequiv "0.000"
53101 53110 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
53841 53850 proof_build_time "0.000"
0 0 p5_p6_equiv "0.000"
53841 53850 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
54431 54440 proof_build_time "0.000"
0 0 swap_noninterfering_assignments "0.000"
54431 54440 proof_check_time "0.000"
55893 55902 proof_build_time "0.000"
0 0 c3 "0.000"
55893 55902 proof_check_time "0.000"
55980 55989 proof_build_time "0.000"
0 0 c4 "0.000"
55980 55989 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
56079 56088 proof_build_time "0.000"
0 0 c3_c4_different "0.000"
56079 56088 proof_check_time "0.000"
56238 56247 proof_build_time "0.000"
0 0 cmin "0.000"
56238 56247 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
56324 56333 proof_build_time "0.000"
0 0 cmin_minimal "0.000"
56324 56333 proof_check_time "0.000"
56552 56561 proof_build_time "0.000"
0 0 zprop "0.000"
56552 56561 proof_check_time "0.000"
0 0 VernacProof "tac:no using:no"
56668 56677 proof_build_time "0.000"
0 0 zprop_preserving "0.000"
56668 56677 proof_check_time "0.000"
0 0 vo_compile_time "0.492"
