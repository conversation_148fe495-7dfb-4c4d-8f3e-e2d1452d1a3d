# This configuration file was generated by running:
# coq_makefile -f _CoqProject -o makefile

COQBIN?=
ifneq (,$(COQBIN))
# add an ending /
COQBIN:=$(COQBIN)/
endif
COQMKFILE ?= "$(COQBIN)coq_makefile"

###############################################################################
#                                                                             #
# Project files.                                                              #
#                                                                             #
###############################################################################

COQMF_CMDLINE_VFILES := 
COQMF_SOURCES := $(shell $(COQMKFILE) -sources-of -f _CoqProject $(COQMF_CMDLINE_VFILES))
COQMF_VFILES := $(filter %.v, $(COQMF_SOURCES))
COQMF_MLIFILES := $(filter %.mli, $(COQMF_SOURCES))
COQMF_MLFILES := $(filter %.ml, $(COQMF_SOURCES))
COQMF_MLGFILES := $(filter %.mlg, $(COQMF_SOURCES))
COQMF_MLPACKFILES := $(filter %.mlpack, $(COQMF_SOURCES))
COQMF_MLLIBFILES := $(filter %.mllib, $(COQMF_SOURCES))
COQMF_METAFILE = 

###############################################################################
#                                                                             #
# Path directives (-I, -R, -Q).                                               #
#                                                                             #
###############################################################################

COQMF_OCAMLLIBS = 
COQMF_SRC_SUBDIRS = 
COQMF_COQLIBS =  -Q . PLF 
COQMF_COQLIBS_NOML = -Q . PLF 
COQMF_CMDLINE_COQLIBS =   

###############################################################################
#                                                                             #
# Coq configuration.                                                          #
#                                                                             #
###############################################################################

COQMF_COQLIB=/home/<USER>/.opam/rocqstar/lib/coq/
COQMF_COQCORELIB=/home/<USER>/.opam/rocqstar/lib/coq/../coq-core/
COQMF_DOCDIR=/home/<USER>/.opam/rocqstar/share/doc/
COQMF_OCAMLFIND=/home/<USER>/.opam/rocqstar/bin/ocamlfind
COQMF_CAMLFLAGS=-thread -bin-annot -strict-sequence -w -a+1..3-4+5..8-9+10..26-27+28..39-40-41-42+43-44-45+46..47-48+49..57-58+59..66-67-68+69-70
COQMF_WARN=-warn-error +a-3
COQMF_HASNATDYNLINK=true
COQMF_COQ_SRC_SUBDIRS=boot config lib clib kernel library engine pretyping interp gramlib parsing proofs tactics toplevel printing ide stm vernac plugins/btauto plugins/cc plugins/derive plugins/extraction plugins/firstorder plugins/funind plugins/ltac plugins/ltac2 plugins/micromega plugins/nsatz plugins/ring plugins/rtauto plugins/ssr plugins/ssrmatching plugins/syntax
COQMF_COQ_NATIVE_COMPILER_DEFAULT=no
COQMF_WINDRIVE=

###############################################################################
#                                                                             #
# Native compiler.                                                            #
#                                                                             #
###############################################################################

COQMF_COQPROJECTNATIVEFLAG = 

###############################################################################
#                                                                             #
# Extra variables.                                                            #
#                                                                             #
###############################################################################

COQMF_OTHERFLAGS = 
COQMF_INSTALLCOQDOCROOT = PLF
